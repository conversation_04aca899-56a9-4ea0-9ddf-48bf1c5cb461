<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserSession;
use App\Services\OktaService;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Session;
use Mockery;
use Tests\TestCase;

class OktaAuthenticationTest extends TestCase
{

    protected $oktaService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->createTables();

        // Mock the OktaService
        $this->oktaService = Mockery::mock(OktaService::class);
        $this->app->instance(OktaService::class, $this->oktaService);
    }

    private function createTables(): void
    {
        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('okta_user_id')->nullable()->unique();
            $table->string('okta_email')->nullable();
            $table->text('okta_profile_data')->nullable(); // Use text for SQLite compatibility
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('user_sessions', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('okta_access_token')->nullable();
            $table->text('okta_refresh_token')->nullable();
            $table->text('okta_id_token')->nullable();
            $table->timestamp('okta_expires_at')->nullable();
            $table->string('app_token_hash')->nullable();
            $table->string('platform')->default('mobile');
            $table->string('okta_session_id')->nullable();
            $table->text('okta_user_data')->nullable(); // Use text for SQLite compatibility
            $table->string('state')->nullable();
            $table->string('code_verifier')->nullable();
            $table->string('code_challenge')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
        });

        Schema::create('personal_access_tokens', function ($table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }

    public function test_login_redirects_to_okta_with_correct_parameters()
    {
        // Mock PKCE generation
        $this->oktaService->shouldReceive('generatePkceChallenge')
            ->once()
            ->andReturn([
                'code_verifier' => 'test_verifier',
                'code_challenge' => 'test_challenge',
            ]);

        // Mock authorization URL building
        $this->oktaService->shouldReceive('buildAuthorizationUrl')
            ->once()
            ->with(Mockery::type('string'), Mockery::type('array'))
            ->andReturn('https://test-domain.okta.com/oauth2/v1/authorize?client_id=test');

        $response = $this->get('/auth/login?platform=mobile');

        $response->assertStatus(302);
        $response->assertRedirect('https://test-domain.okta.com/oauth2/v1/authorize?client_id=test');
        
        // Check session data
        $this->assertNotNull(session('oauth_state'));
        $this->assertNotNull(session('code_verifier'));
        $this->assertEquals('mobile', session('auth_platform'));
    }

    public function test_callback_creates_user_and_session_successfully()
    {
        // Set up session data
        Session::put([
            'oauth_state' => 'test_state',
            'code_verifier' => 'test_verifier',
            'auth_platform' => 'mobile',
        ]);

        // Mock token exchange
        $this->oktaService->shouldReceive('exchangeCodeForTokens')
            ->once()
            ->with('test_code', 'test_verifier')
            ->andReturn([
                'access_token' => 'test_access_token',
                'refresh_token' => 'test_refresh_token',
                'id_token' => 'test_id_token',
                'expires_in' => 3600,
            ]);

        // Mock user profile fetch
        $this->oktaService->shouldReceive('getUserProfile')
            ->once()
            ->with('test_access_token')
            ->andReturn([
                'sub' => 'okta_user_123',
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'email_verified' => true,
            ]);

        $response = $this->get('/auth/callback?code=test_code&state=test_state');

        $response->assertStatus(302);
        $response->assertRedirect();
        
        // Check that user was created
        $this->assertDatabaseHas('users', [
            'okta_user_id' => 'okta_user_123',
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Check that user session was created
        $user = User::where('okta_user_id', 'okta_user_123')->first();
        $this->assertDatabaseHas('user_sessions', [
            'user_id' => $user->id,
            'platform' => 'mobile',
            'is_active' => true,
        ]);
    }

    public function test_callback_handles_invalid_state_parameter()
    {
        Session::put([
            'oauth_state' => 'correct_state',
            'code_verifier' => 'test_verifier',
            'auth_platform' => 'mobile',
        ]);

        $response = $this->get('/auth/callback?code=test_code&state=wrong_state');

        $response->assertStatus(302);
        $response->assertRedirect();
        
        // Should redirect to error page
        $redirectUrl = $response->headers->get('Location');
        $this->assertStringContainsString('auth/error', $redirectUrl);
    }

    public function test_user_endpoint_returns_authenticated_user_data()
    {
        $user = User::factory()->create([
            'okta_user_id' => 'okta_user_123',
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->get('/api/user');

        $response->assertStatus(200);
        $response->assertJson([
            'id' => $user->id,
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'okta_user_id' => 'okta_user_123',
        ]);
    }

    public function test_refresh_token_generates_new_app_token()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;
        
        // Create user session
        $userSession = UserSession::create([
            'user_id' => $user->id,
            'okta_access_token' => 'encrypted_access_token',
            'okta_refresh_token' => 'encrypted_refresh_token',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', $token),
            'platform' => 'mobile',
            'is_active' => true,
            'last_activity_at' => now(),
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->post('/api/refresh');

        $response->assertStatus(200);
        $response->assertJsonStructure(['token']);
        
        // Verify new token is different
        $newToken = $response->json('token');
        $this->assertNotEquals($token, $newToken);
    }

    public function test_logout_deactivates_session_and_revokes_tokens()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;
        
        // Create user session
        $userSession = UserSession::create([
            'user_id' => $user->id,
            'okta_access_token' => 'encrypted_access_token',
            'okta_refresh_token' => 'encrypted_refresh_token',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', $token),
            'platform' => 'mobile',
            'is_active' => true,
            'last_activity_at' => now(),
        ]);

        // Mock token revocation
        $this->oktaService->shouldReceive('revokeToken')
            ->twice()
            ->andReturn(true);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->post('/api/logout');

        $response->assertStatus(200);
        $response->assertJson(['message' => 'Successfully logged out']);
        
        // Check that session was deactivated
        $userSession->refresh();
        $this->assertFalse($userSession->is_active);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
