<?php

namespace Tests;

use Illuminate\Contracts\Console\Kernel;
use Illuminate\Foundation\Testing\RefreshDatabaseState;
use Illuminate\Support\Facades\Schema;

trait RefreshInMemoryDatabase
{
    /**
     * Define hooks to migrate the database before and after each test.
     */
    public function refreshInMemoryDatabase(): void
    {
        $this->artisan('db:wipe', [
            '--database' => 'sqlite',
            '--drop-views' => true,
        ]);

        $this->createTables();

        $this->app[Kernel::class]->setArtisan(null);
    }

    /**
     * Begin a database transaction on the testing database.
     */
    public function beginDatabaseTransaction(): void
    {
        $database = $this->app->make('db');

        foreach ($this->connectionsToTransact() as $name) {
            $connection = $database->connection($name);
            $dispatcher = $connection->getEventDispatcher();

            $connection->unsetEventDispatcher();
            $connection->beginTransaction();
            $connection->setEventDispatcher($dispatcher);
        }

        $this->beforeApplicationDestroyed(function () use ($database) {
            foreach ($this->connectionsToTransact() as $name) {
                $connection = $database->connection($name);
                $dispatcher = $connection->getEventDispatcher();

                $connection->unsetEventDispatcher();
                $connection->rollback();
                $connection->setEventDispatcher($dispatcher);
                $connection->disconnect();
            }
        });
    }

    /**
     * The database connections that should have transactions.
     */
    protected function connectionsToTransact(): array
    {
        return property_exists($this, 'connectionsToTransact')
                            ? $this->connectionsToTransact : [null];
    }

    /**
     * Create the database tables for testing.
     */
    protected function createTables(): void
    {
        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('okta_user_id')->nullable()->unique();
            $table->string('okta_email')->nullable();
            $table->json('okta_profile_data')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('user_sessions', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('okta_access_token')->nullable();
            $table->text('okta_refresh_token')->nullable();
            $table->text('okta_id_token')->nullable();
            $table->timestamp('okta_expires_at')->nullable();
            $table->string('app_token_hash')->nullable();
            $table->string('platform')->default('mobile');
            $table->string('okta_session_id')->nullable();
            $table->json('okta_user_data')->nullable();
            $table->string('state')->nullable();
            $table->string('code_verifier')->nullable();
            $table->string('code_challenge')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
        });

        Schema::create('personal_access_tokens', function ($table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }
}
