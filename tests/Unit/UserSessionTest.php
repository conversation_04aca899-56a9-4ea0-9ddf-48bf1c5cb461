<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class UserSessionTest extends TestCase
{

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTables();
    }

    private function createTables(): void
    {
        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('okta_user_id')->nullable()->unique();
            $table->string('okta_email')->nullable();
            $table->text('okta_profile_data')->nullable(); // Use text for SQLite compatibility
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('user_sessions', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('okta_access_token')->nullable();
            $table->text('okta_refresh_token')->nullable();
            $table->text('okta_id_token')->nullable();
            $table->timestamp('okta_expires_at')->nullable();
            $table->string('app_token_hash')->nullable();
            $table->string('platform')->default('mobile');
            $table->string('okta_session_id')->nullable();
            $table->text('okta_user_data')->nullable(); // Use text for SQLite compatibility
            $table->string('state')->nullable();
            $table->string('code_verifier')->nullable();
            $table->string('code_challenge')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
        });

        Schema::create('personal_access_tokens', function ($table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }

    public function test_user_session_encrypts_okta_tokens()
    {
        $user = User::factory()->create();
        
        $userSession = UserSession::create([
            'user_id' => $user->id,
            'okta_access_token' => 'plain_access_token',
            'okta_refresh_token' => 'plain_refresh_token',
            'okta_id_token' => 'plain_id_token',
            'okta_expires_at' => now()->addHour(),
            'platform' => 'mobile',
            'is_active' => true,
        ]);

        // Check that tokens are encrypted in database
        $rawData = \DB::table('user_sessions')->where('id', $userSession->id)->first();
        $this->assertNotEquals('plain_access_token', $rawData->okta_access_token);
        $this->assertNotEquals('plain_refresh_token', $rawData->okta_refresh_token);
        $this->assertNotEquals('plain_id_token', $rawData->okta_id_token);

        // Check that tokens are decrypted when accessed
        $this->assertEquals('plain_access_token', $userSession->okta_access_token);
        $this->assertEquals('plain_refresh_token', $userSession->okta_refresh_token);
        $this->assertEquals('plain_id_token', $userSession->okta_id_token);
    }

    public function test_is_okta_session_valid_returns_correct_status()
    {
        $user = User::factory()->create();
        
        // Valid session
        $validSession = UserSession::create([
            'user_id' => $user->id,
            'okta_expires_at' => now()->addHour(),
            'is_active' => true,
        ]);
        
        $this->assertTrue($validSession->isOktaSessionValid());

        // Expired session
        $expiredSession = UserSession::create([
            'user_id' => $user->id,
            'okta_expires_at' => now()->subHour(),
            'is_active' => true,
        ]);
        
        $this->assertFalse($expiredSession->isOktaSessionValid());

        // Inactive session
        $inactiveSession = UserSession::create([
            'user_id' => $user->id,
            'okta_expires_at' => now()->addHour(),
            'is_active' => false,
        ]);
        
        $this->assertFalse($inactiveSession->isOktaSessionValid());
    }

    public function test_deactivate_sets_session_inactive()
    {
        $user = User::factory()->create();
        
        $userSession = UserSession::create([
            'user_id' => $user->id,
            'is_active' => true,
        ]);

        $this->assertTrue($userSession->is_active);
        
        $userSession->deactivate();
        
        $this->assertFalse($userSession->fresh()->is_active);
    }

    public function test_update_activity_updates_timestamp()
    {
        $user = User::factory()->create();
        
        $userSession = UserSession::create([
            'user_id' => $user->id,
            'last_activity_at' => now()->subHour(),
        ]);

        $oldActivity = $userSession->last_activity_at;
        
        $userSession->updateActivity();
        
        $this->assertNotEquals($oldActivity, $userSession->fresh()->last_activity_at);
    }

    public function test_scopes_work_correctly()
    {
        $user = User::factory()->create();
        
        // Create active and inactive sessions
        $activeSession = UserSession::create([
            'user_id' => $user->id,
            'is_active' => true,
            'platform' => 'mobile',
            'okta_expires_at' => now()->addHour(),
        ]);
        
        $inactiveSession = UserSession::create([
            'user_id' => $user->id,
            'is_active' => false,
            'platform' => 'web',
            'okta_expires_at' => now()->subHour(),
        ]);

        // Test active scope
        $activeSessions = UserSession::active()->get();
        $this->assertCount(1, $activeSessions);
        $this->assertEquals($activeSession->id, $activeSessions->first()->id);

        // Test platform scope
        $mobileSessions = UserSession::forPlatform('mobile')->get();
        $this->assertCount(1, $mobileSessions);
        $this->assertEquals($activeSession->id, $mobileSessions->first()->id);

        // Test expired scope
        $expiredSessions = UserSession::expired()->get();
        $this->assertCount(1, $expiredSessions);
        $this->assertEquals($inactiveSession->id, $expiredSessions->first()->id);
    }

    public function test_user_relationship_works()
    {
        $user = User::factory()->create(['name' => 'Test User']);
        
        $userSession = UserSession::create([
            'user_id' => $user->id,
        ]);

        $this->assertEquals('Test User', $userSession->user->name);
    }
}
