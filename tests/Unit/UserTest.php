<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class UserTest extends TestCase
{

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTables();
    }

    private function createTables(): void
    {
        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('okta_user_id')->nullable()->unique();
            $table->string('okta_email')->nullable();
            $table->text('okta_profile_data')->nullable(); // Use text for SQLite compatibility
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('user_sessions', function ($table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('okta_access_token')->nullable();
            $table->text('okta_refresh_token')->nullable();
            $table->text('okta_id_token')->nullable();
            $table->timestamp('okta_expires_at')->nullable();
            $table->string('app_token_hash')->nullable();
            $table->string('platform')->default('mobile');
            $table->string('okta_session_id')->nullable();
            $table->text('okta_user_data')->nullable(); // Use text for SQLite compatibility
            $table->string('state')->nullable();
            $table->string('code_verifier')->nullable();
            $table->string('code_challenge')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
        });

        Schema::create('personal_access_tokens', function ($table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }

    public function test_create_or_update_from_okta_creates_new_user()
    {
        $oktaProfile = [
            'sub' => 'okta_user_123',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'email_verified' => true,
            'preferred_username' => 'johndoe',
        ];

        $user = User::createOrUpdateFromOkta($oktaProfile);

        $this->assertDatabaseHas('users', [
            'okta_user_id' => 'okta_user_123',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'okta_email' => '<EMAIL>',
        ]);

        $this->assertEquals($oktaProfile, $user->okta_profile_data);
        $this->assertNotNull($user->email_verified_at);
    }

    public function test_create_or_update_from_okta_updates_existing_user()
    {
        // Create existing user
        $existingUser = User::factory()->create([
            'okta_user_id' => 'okta_user_123',
            'name' => 'Old Name',
            'email' => '<EMAIL>',
        ]);

        $oktaProfile = [
            'sub' => 'okta_user_123',
            'name' => 'New Name',
            'email' => '<EMAIL>',
            'email_verified' => true,
        ];

        $user = User::createOrUpdateFromOkta($oktaProfile);

        // Should be the same user (updated)
        $this->assertEquals($existingUser->id, $user->id);
        
        $this->assertDatabaseHas('users', [
            'id' => $existingUser->id,
            'okta_user_id' => 'okta_user_123',
            'name' => 'New Name',
            'email' => '<EMAIL>',
        ]);
    }

    public function test_create_or_update_from_okta_handles_missing_fields()
    {
        $oktaProfile = [
            'sub' => 'okta_user_123',
            'preferred_username' => 'johndoe',
            // Missing name and email
        ];

        $user = User::createOrUpdateFromOkta($oktaProfile);

        $this->assertDatabaseHas('users', [
            'okta_user_id' => 'okta_user_123',
            'name' => 'johndoe', // Should use preferred_username as fallback
            'email' => 'johndoe', // Should use preferred_username as fallback
            'okta_email' => null,
        ]);
    }

    public function test_user_sessions_relationship()
    {
        $user = User::factory()->create();
        
        $session1 = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'is_active' => true,
        ]);
        
        $session2 = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'web',
            'is_active' => false,
        ]);

        $this->assertCount(2, $user->userSessions);
        $this->assertCount(1, $user->activeUserSessions);
    }

    public function test_get_active_session_for_platform()
    {
        $user = User::factory()->create();
        
        // Create multiple sessions for different platforms
        $mobileSession1 = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'is_active' => true,
            'last_activity_at' => now()->subHour(),
        ]);
        
        $mobileSession2 = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'is_active' => true,
            'last_activity_at' => now(), // More recent
        ]);
        
        $webSession = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'web',
            'is_active' => true,
            'last_activity_at' => now(),
        ]);

        // Should return the most recent mobile session
        $activeSession = $user->getActiveSessionForPlatform('mobile');
        $this->assertEquals($mobileSession2->id, $activeSession->id);

        // Should return the web session
        $webActiveSession = $user->getActiveSessionForPlatform('web');
        $this->assertEquals($webSession->id, $webActiveSession->id);

        // Should return null for non-existent platform
        $nonExistentSession = $user->getActiveSessionForPlatform('desktop');
        $this->assertNull($nonExistentSession);
    }

    public function test_okta_profile_data_is_cast_to_array()
    {
        $user = User::factory()->create([
            'okta_profile_data' => ['key' => 'value', 'nested' => ['data' => 'test']],
        ]);

        $this->assertIsArray($user->okta_profile_data);
        $this->assertEquals('value', $user->okta_profile_data['key']);
        $this->assertEquals('test', $user->okta_profile_data['nested']['data']);
    }

    public function test_password_is_nullable_for_okta_users()
    {
        $user = User::create([
            'name' => 'Okta User',
            'email' => '<EMAIL>',
            'okta_user_id' => 'okta_123',
            'password' => null, // Should be allowed
        ]);

        $this->assertNull($user->password);
        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'password' => null,
        ]);
    }
}
