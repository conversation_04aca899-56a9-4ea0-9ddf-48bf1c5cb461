<?php

namespace Tests\Unit;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class DatabaseTest extends TestCase
{
    public function test_database_connection_works()
    {
        // Test basic database connection
        $result = DB::select('SELECT 1 as test');
        
        $this->assertEquals(1, $result[0]->test);
    }

    public function test_can_create_table()
    {
        Schema::create('test_table', function ($table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        $this->assertTrue(Schema::hasTable('test_table'));
        
        // Clean up
        Schema::dropIfExists('test_table');
    }

    public function test_can_insert_and_retrieve_data()
    {
        Schema::create('test_users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->timestamps();
        });

        DB::table('test_users')->insert([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $user = DB::table('test_users')->where('email', '<EMAIL>')->first();
        
        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        
        // Clean up
        Schema::dropIfExists('test_users');
    }
}
