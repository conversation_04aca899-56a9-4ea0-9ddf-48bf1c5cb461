<?php

namespace Tests\Unit;

use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class UserBasicTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->createTables();
    }

    private function createTables(): void
    {
        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('okta_user_id')->nullable()->unique();
            $table->string('okta_email')->nullable();
            $table->text('okta_profile_data')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    public function test_can_create_basic_user()
    {
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
    }

    public function test_can_create_user_with_json_data()
    {
        $profileData = ['key' => 'value', 'nested' => ['data' => 'test']];
        
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'okta_profile_data' => json_encode($profileData), // Manually encode
        ]);

        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        
        // The cast should decode it back to array
        $this->assertIsArray($user->okta_profile_data);
        $this->assertEquals('value', $user->okta_profile_data['key']);
    }

    public function test_can_create_user_with_array_data()
    {
        $profileData = ['key' => 'value', 'nested' => ['data' => 'test']];
        
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'okta_profile_data' => $profileData, // Pass array directly
        ]);

        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        
        // The cast should handle the array
        $this->assertIsArray($user->okta_profile_data);
        $this->assertEquals('value', $user->okta_profile_data['key']);
    }


}
