<?php

namespace Tests\Unit;

use App\Services\OktaService;
use Tests\TestCase;

class OktaServiceBasicTest extends TestCase
{
    public function test_okta_service_can_be_instantiated()
    {
        // Set test configuration
        config([
            'services.okta.domain' => 'test-domain.okta.com',
            'services.okta.client_id' => 'test_client_id',
            'services.okta.client_secret' => 'test_client_secret',
            'services.okta.redirect_uri' => 'http://localhost/auth/callback',
        ]);

        $service = new OktaService();
        
        $this->assertInstanceOf(OktaService::class, $service);
    }

    public function test_pkce_challenge_generation_works()
    {
        // Set test configuration
        config([
            'services.okta.domain' => 'test-domain.okta.com',
            'services.okta.client_id' => 'test_client_id',
            'services.okta.client_secret' => 'test_client_secret',
            'services.okta.redirect_uri' => 'http://localhost/auth/callback',
        ]);

        $service = new OktaService();
        $pkce = $service->generatePkceChallenge();

        $this->assertIsArray($pkce);
        $this->assertArrayHasKey('code_verifier', $pkce);
        $this->assertArrayHasKey('code_challenge', $pkce);
        $this->assertEquals(128, strlen($pkce['code_verifier']));
        $this->assertNotEmpty($pkce['code_challenge']);
    }

    public function test_authorization_url_building_works()
    {
        // Set test configuration
        config([
            'services.okta.domain' => 'test-domain.okta.com',
            'services.okta.client_id' => 'test_client_id',
            'services.okta.client_secret' => 'test_client_secret',
            'services.okta.redirect_uri' => 'http://localhost/auth/callback',
        ]);

        $service = new OktaService();
        $state = 'test_state';
        $pkce = ['code_challenge' => 'test_challenge'];

        $url = $service->buildAuthorizationUrl($state, $pkce);

        $this->assertStringContainsString('https://test-domain.okta.com/oauth2/v1/authorize', $url);
        $this->assertStringContainsString('client_id=test_client_id', $url);
        $this->assertStringContainsString('response_type=code', $url);
        $this->assertStringContainsString('scope=openid+profile+email', $url);
        $this->assertStringContainsString('state=test_state', $url);
        $this->assertStringContainsString('code_challenge=test_challenge', $url);
        $this->assertStringContainsString('code_challenge_method=S256', $url);
    }
}
