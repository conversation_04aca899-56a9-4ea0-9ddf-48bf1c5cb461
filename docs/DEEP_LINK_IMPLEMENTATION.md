# Deep Link Redirect Authentication Implementation

This document describes the implementation of deep link redirect authentication for the Laravel middleware, as requested for React Native app integration.

## Overview

The implementation replaces the previous WebView-based authentication flow with a deep link redirect approach. After successful Okta authentication, the middleware creates a short-lived JWT token and redirects the user to the mobile app via a deep link URL.

## Architecture Flow

```
1. React Native App → WebView → http://middleware.local/auth/login
2. Middleware → Okta SSO (user authenticates)
3. Okta → http://middleware.local/sso-auth/callback?code=...&state=...
4. Middleware exchanges code server-side
5. Middleware creates short-lived JWT token (60s expiry)
6. Middleware → 302 redirect to myapp://auth-callback?token=...&user=...
7. React Native App intercepts deep link and processes token
```

## Key Components Implemented

### 1. Configuration Updates

**config/services.php**
- Added `issuer` configuration for Ok<PERSON>
- Updated to support both domain and issuer URL formats

**config/app.php**
- Added `auth_callback_path` configuration
- Added `token_signing_key` for JWT signing

**Environment Variables (.env)**
```env
# Okta Configuration
OKTA_ISSUER=https://YOUR_OKTA_DOMAIN/oauth2/default
OKTA_CLIENT_ID=xxxxxxxx
OKTA_CLIENT_SECRET=xxxxxxxx
OKTA_REDIRECT_URI=http://middleware.local/sso-auth/callback

# App Scheme and Auth Callback Path Configuration
APP_SCHEME=myapp
APP_AUTH_CALLBACK_PATH=auth-callback
# For Expo development, this becomes: exp://192.168.1.100:8081/--/auth-callback
# For production builds, this becomes: myapp://auth-callback

# Token Signing Key
TOKEN_SIGNING_KEY=base64:hFUgqM1sr1VcMI9UuGtaNHwXPTBT1TUTpiCbN9cxGUM=
```

### 2. AuthController Updates

**New callback() method implementation:**
- Direct HTTP calls to Okta (as per requirements)
- Server-side token exchange using PKCE
- Short-lived JWT token creation (60-second expiry)
- Deep link redirect with token and user data
- Comprehensive error handling with deep link error redirects

**New helper methods:**
- `createShortLivedToken()` - Creates JWT with 60s expiry
- `buildDeepLinkUrl()` - Constructs deep link with token and user data
- `handleCallbackError()` - Redirects to deep link with error parameters

### 3. Dependencies

**Added Firebase JWT library:**
```bash
composer require firebase/php-jwt:^6.0
```

### 4. Security Features

- **Short-lived tokens**: JWT tokens expire in 60 seconds
- **PKCE validation**: Code verifier validation for security
- **State parameter validation**: Prevents CSRF attacks
- **Secure token signing**: Uses Laravel's encryption key for JWT signing
- **Error handling**: All errors redirect to deep link with error parameters

## Usage Examples

### Successful Authentication
```
Deep link: myapp://auth-callback?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...&user=%7B%22sub%22%3A%22user123%22%2C%22email%22%3A%22user%40example.com%22%2C%22name%22%3A%22John%20Doe%22%7D
```

### Error Handling
```
Deep link: myapp://auth-callback?error=Invalid%20state%20parameter
```

## React Native Integration

The React Native app should:

1. **Intercept deep links** in `AuthWebView.tsx` using `onShouldStartLoadWithRequest()`
2. **Extract token and user data** from the deep link URL
3. **Validate and store the token** using expo-secure-store
4. **Handle errors** by parsing the error parameter

## Testing

Comprehensive test suite included in `tests/Feature/DeepLinkAuthTest.php`:
- ✅ Successful token creation and deep link redirect
- ✅ Missing authorization code error handling
- ✅ Invalid state parameter error handling
- ✅ JWT token validation and expiry verification

## Development vs Production

**Development (Expo):**
```env
APP_SCHEME=exp
APP_AUTH_CALLBACK_PATH=192.168.1.100:8081/--/auth-callback
# Results in: exp://192.168.1.100:8081/--/auth-callback
```

**Production:**
```env
APP_SCHEME=myapp
APP_AUTH_CALLBACK_PATH=auth-callback
# Results in: myapp://auth-callback
```

## Security Considerations

1. **Token Expiry**: 60-second expiry minimizes exposure risk
2. **HTTPS Required**: Use HTTPS in production for all middleware endpoints
3. **Key Management**: Rotate TOKEN_SIGNING_KEY regularly
4. **Logging**: Minimal PII logging in production
5. **Deep Link Validation**: App should validate JWT signature and expiry

## Troubleshooting

**Common Issues:**
- Ensure deep link scheme matches app configuration
- Verify TOKEN_SIGNING_KEY is properly base64 encoded
- Check Okta redirect URI matches exactly
- Confirm React Native app can handle the deep link scheme

**Debugging:**
- Check Laravel logs for authentication flow details
- Verify JWT token can be decoded with the signing key
- Test deep link handling in React Native app
