# Token Exchange Flow Documentation

## Overview

The token exchange endpoint (`/api/exchange-token`) completes the Okta SSO authentication flow by converting the short-lived JWT token received via deep link into a Laravel Sanctum API token that the app can use for ongoing API calls.

## Complete Authentication Flow

### 1. App Initiates Authentication
```
React Native App → WebView → GET /auth/login?platform=mobile
```

### 2. Okta SSO Flow
```
Middleware → Okta SSO (user authenticates)
Okta → Middleware callback: GET /sso-auth/callback?code=...&state=...
```

### 3. Token Creation & Deep Link
```
Middleware:
- Exchanges code for Okta tokens
- Creates/updates user in database
- Creates short-lived JWT (60 seconds)
- Redirects to: myapp://auth-callback?token=JWT&user=...
```

### 4. Token Exchange (NEW)
```
React Native App:
- Intercepts deep link
- Extracts JWT token
- Calls: POST /auth/exchange-token with JWT
- Receives Laravel Sanctum token for API calls
```

## API Endpoints

### POST /auth/exchange-token

**Purpose**: Exchange short-lived JWT for Laravel Sanctum API token

**Request**:
```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Success Response** (200):
```json
{
  "message": "Token exchanged successfully",
  "session": "1|abc123def456..."
}
```

**Error Responses**:
- `400`: Missing token
- `401`: Invalid/expired token
- `404`: User not found

## Token Types Comparison

| Token Type | Purpose | Lifetime | Usage |
|------------|---------|----------|-------|
| **Short-lived JWT** | Deep link authentication | 60 seconds | One-time exchange only |
| **Laravel Sanctum Token** | API authentication | Until logout/revoked | All API calls |
| **Okta Tokens** | SSO session management | Per Okta config | Internal middleware use |

## Security Features

### JWT Token Security
- **Short expiration**: 60 seconds to minimize exposure
- **Signed with app key**: Prevents tampering
- **One-time use**: Should be exchanged immediately
- **Contains minimal data**: Only user identification

### Sanctum Token Security
- **Long-lived**: Until explicitly revoked
- **Database-backed**: Can be revoked server-side
- **Scoped**: Can have specific abilities/permissions
- **Rate limited**: Protected by Laravel's rate limiting

## Implementation Details

### JWT Payload Structure
```json
{
  "sub": "okta_user_123",
  "email": "<EMAIL>", 
  "name": "John Doe",
  "exp": 1234567890,
  "iat": 1234567830
}
```

### Error Handling
The endpoint handles various error scenarios:
- **Malformed JWT**: Returns 401 with "Invalid token"
- **Expired JWT**: Returns 401 with "Token expired" 
- **Missing user**: Returns 404 with "User not found"
- **Invalid signature**: Returns 401 with "Invalid token signature"

### Logging
All token exchange attempts are logged with:
- User identification (sub, email)
- Token expiration times
- Success/failure status
- Error details for debugging

## React Native Integration

### 1. Handle Deep Link
```javascript
// In your deep link handler
const handleDeepLink = (url) => {
  const params = parseURL(url);
  if (params.token) {
    exchangeTokenForSession(params.token);
  }
};
```

### 2. Exchange Token
```javascript
const exchangeTokenForSession = async (jwtToken) => {
  try {
    const response = await fetch('/auth/exchange-token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token: jwtToken })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      // Store the session token for API calls
      await SecureStore.setItemAsync('session_token', data.session);
      // Navigate to authenticated screens
      navigation.navigate('Home');
    } else {
      // Handle error
      console.error('Token exchange failed:', data.error);
    }
  } catch (error) {
    console.error('Token exchange error:', error);
  }
};
```

### 3. Use Session Token for API Calls
```javascript
const makeAuthenticatedRequest = async (endpoint) => {
  const sessionToken = await SecureStore.getItemAsync('session_token');
  
  return fetch(endpoint, {
    headers: {
      'Authorization': `Bearer ${sessionToken}`,
      'Content-Type': 'application/json'
    }
  });
};
```

## Comparison with Dev Login

Both authentication methods now return the same token structure:

### Dev Login Response
```json
{
  "message": "Logged in as user",
  "session": "1|abc123def456..."
}
```

### Token Exchange Response  
```json
{
  "message": "Token exchanged successfully", 
  "session": "1|abc123def456..."
}
```

This ensures consistent handling in the React Native app regardless of authentication method.

## Testing

The implementation includes comprehensive tests covering:
- ✅ Successful token exchange
- ✅ Missing token validation
- ✅ Invalid token handling
- ✅ Expired token rejection
- ✅ User not found scenarios
- ✅ Response structure consistency with dev login

Run tests with:
```bash
php artisan test tests/Feature/TokenExchangeTest.php
```
