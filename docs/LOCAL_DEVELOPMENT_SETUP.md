# Local Development Setup - Okta SSO Authentication

This guide helps team members set up local development for the middleware API with Okta SSO authentication.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- Git repository cloned
- Basic terminal/command prompt access

## One-Time Setup (Required for Each Developer)

### Step 1: Add Custom Domain to Hosts File

This allows your local machine to resolve `middleware.local` to your local Docker containers.

#### For Mac/Linux Users:

```bash
# Open hosts file with admin privileges
sudo nano /etc/hosts

# Add this line at the end of the file:
127.0.0.1 middleware.local

# Save and exit (Ctrl+X, then Y, then Enter in nano)
```

#### For Windows Users:

```bash
# Open Command Prompt as Administrator
# (Right-click Command Prompt → "Run as administrator")

# Open hosts file in notepad
notepad C:\Windows\System32\drivers\etc\hosts

# Add this line at the end of the file:
127.0.0.1 middleware.local

# Save and close notepad
```

### Step 2: Verify Setup

After adding the hosts entry, test that it works:

```bash
# This should resolve to 127.0.0.1
ping middleware.local
```

## Daily Development Workflow

### Starting the Middleware

```bash
# Navigate to the middleware project
cd /path/to/middleware-api

# Start all services
./middleware start

# Verify it's running - you should see a 302 redirect
curl -I http://middleware.local/auth/login
```

### Stopping the Middleware

```bash
# Stop all services
docker-compose -f docker-compose.dev.yaml down
```

## React Native App Configuration

### Update Your Auth Service

In your React Native app, update the auth URL to use the custom domain:

```javascript
// ✅ CORRECT - Use middleware.local
const authUrl = 'http://middleware.local/auth/login';

// ❌ WRONG - Don't use localhost or IP addresses
// const authUrl = 'http://localhost/auth/login';
// const authUrl = 'http://192.168.x.x/auth/login';
```

### Example WebView Integration

```javascript
import { WebView } from 'react-native-webview';
import * as SecureStore from 'expo-secure-store';

const AuthWebView = ({ onAuthSuccess, onAuthError }) => {
  const handleNavigationStateChange = (navState) => {
    const { url } = navState;
    
    // Check for success redirect
    if (url.includes('learningcoachcommunity://auth/success')) {
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const token = urlParams.get('token');
      const userData = JSON.parse(urlParams.get('user') || '{}');
      
      // Store token securely
      SecureStore.setItemAsync('auth_token', token);
      SecureStore.setItemAsync('user_data', JSON.stringify(userData));
      
      onAuthSuccess({ token, user: userData });
    }
    
    // Check for error redirect
    if (url.includes('learningcoachcommunity://auth/error')) {
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const error = urlParams.get('error');
      onAuthError(error);
    }
  };

  return (
    <WebView
      source={{ uri: 'http://middleware.local/auth/login' }}
      onNavigationStateChange={handleNavigationStateChange}
      startInLoadingState={true}
      javaScriptEnabled={true}
      domStorageEnabled={true}
    />
  );
};
```

## Authentication Flow Overview

Understanding the complete flow helps with debugging:

```
1. React Native App → http://middleware.local/auth/login
2. Middleware → Okta SSO (user authenticates)
3. Okta → http://middleware.local/sso-auth/callback
4. Middleware processes tokens and creates user session
5. Middleware → learningcoachcommunity://auth/success?token=...
6. React Native App receives token and user data
```

## Route Architecture

The middleware uses two distinct route prefixes:

- **`/auth/`** - App-to-Middleware communication
  - `GET /auth/login` - Start authentication
  - `GET /auth/success` - Success page (web platform)
  - `GET /auth/error` - Error page (web platform)
  - `POST /auth/refresh` - Refresh app tokens

- **`/sso-auth/`** - Middleware-to-SSO Provider communication
  - `GET /sso-auth/callback` - Okta callback handler

## Troubleshooting

### Common Issues

#### 1. "Could not resolve host: middleware.local"

**Problem:** Hosts file entry not added or incorrect.

**Solution:** 
- Verify the hosts file entry: `127.0.0.1 middleware.local`
- On Windows, ensure you ran Command Prompt as Administrator
- Try `ping middleware.local` to test DNS resolution

#### 2. "Connection refused" or 502 errors

**Problem:** Docker containers not running or not healthy.

**Solution:**
```bash
# Check container status
docker ps

# Restart if needed
./middleware start

# Check logs if issues persist
docker logs middleware-api-web-1
docker logs middleware-api-php-fpm-1
```

#### 3. "Could not connect to the server" in React Native

**Problem:** Device/simulator can't reach the middleware.

**Solution:**
- Ensure you're using `middleware.local` not `localhost`
- Verify hosts file is correctly configured
- Test with `curl -I http://middleware.local/auth/login` from terminal

#### 4. Authentication redirects to wrong URL

**Problem:** Okta configuration mismatch.

**Solution:**
- Verify `.env` file has: `OKTA_REDIRECT_URI=http://middleware.local/sso-auth/callback`
- Clear Laravel config cache: `docker exec middleware-api-php-fpm-1 php artisan config:clear`
- Restart containers if needed

### Getting Help

If you encounter issues:

1. **Check container logs:**
   ```bash
   docker logs middleware-api-web-1
   docker logs middleware-api-php-fpm-1
   ```

2. **Verify configuration:**
   ```bash
   # Check if middleware.local resolves
   ping middleware.local
   
   # Test auth endpoint
   curl -I http://middleware.local/auth/login
   ```

3. **Clear caches:**
   ```bash
   docker exec middleware-api-php-fpm-1 php artisan config:clear
   docker exec middleware-api-php-fpm-1 php artisan cache:clear
   ```

## Team Onboarding Checklist

For new team members:

- [ ] Clone the repository
- [ ] Install Docker and Docker Compose
- [ ] Add `127.0.0.1 middleware.local` to hosts file
- [ ] Run `./middleware start`
- [ ] Test with `curl -I http://middleware.local/auth/login`
- [ ] Update React Native app to use `middleware.local`
- [ ] Test full authentication flow

## Security Notes

- The `middleware.local` domain only works locally and is not accessible from outside your machine
- Okta tokens are encrypted and stored server-side only
- App tokens are generated by the middleware for React Native communication
- All authentication data is isolated per developer environment

---

**Questions?** Ask in the team chat or create an issue in the repository.
