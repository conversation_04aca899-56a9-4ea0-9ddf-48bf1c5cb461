// React Native Okta Authentication Example
// This is a basic example showing how to integrate with the Laravel middleware

import React, { useState, useEffect } from 'react';
import { View, Text, Button, Alert, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import * as SecureStore from 'expo-secure-store';

// Configuration
const MIDDLEWARE_URL = __DEV__ 
  ? 'http://localhost:8000' 
  : 'https://your-middleware.com';

const APP_SCHEME = 'myapp';

// Platform-specific storage utility
const SecureStorage = {
  async setItem(key, value) {
    if (Platform.OS === 'web') {
      // Web: Use localStorage or sessionStorage
      localStorage.setItem(key, value);
    } else {
      // Mobile: Use expo-secure-store
      await SecureStore.setItemAsync(key, value);
    }
  },

  async getItem(key) {
    if (Platform.OS === 'web') {
      return localStorage.getItem(key);
    } else {
      return await SecureStore.getItemAsync(key);
    }
  },

  async removeItem(key) {
    if (Platform.OS === 'web') {
      localStorage.removeItem(key);
    } else {
      await SecureStore.deleteItemAsync(key);
    }
  }
};

// Auth WebView Component (for mobile)
const AuthWebView = ({ onAuthSuccess, onAuthError, onCancel }) => {
  const handleNavigationStateChange = (navState) => {
    const { url } = navState;
    
    // Check for success redirect
    if (url.includes(`${APP_SCHEME}://auth/success`)) {
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const token = urlParams.get('token');
      const userData = JSON.parse(urlParams.get('user') || '{}');
      
      if (token && userData) {
        onAuthSuccess({ token, user: userData });
      } else {
        onAuthError('Invalid authentication response');
      }
    }
    
    // Check for error redirect
    if (url.includes(`${APP_SCHEME}://auth/error`)) {
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const error = urlParams.get('error');
      onAuthError(error || 'Authentication failed');
    }

    // Check for cancel
    if (url.includes(`${APP_SCHEME}://auth/cancel`)) {
      onCancel();
    }
  };

  return (
    <WebView
      source={{ uri: `${MIDDLEWARE_URL}/auth/login` }}
      onNavigationStateChange={handleNavigationStateChange}
      startInLoadingState={true}
      javaScriptEnabled={true}
      domStorageEnabled={true}
      style={{ flex: 1 }}
    />
  );
};

// Main Auth Hook
const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check for existing authentication on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const storedToken = await SecureStorage.getItem('auth_token');
      const storedUser = await SecureStorage.getItem('user_data');

      if (storedToken && storedUser) {
        // Verify token is still valid
        const response = await fetch(`${MIDDLEWARE_URL}/api/user`, {
          headers: {
            'Authorization': `Bearer ${storedToken}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const userData = await response.json();
          setToken(storedToken);
          setUser(userData);
          setIsAuthenticated(true);
        } else {
          // Token is invalid, clear storage
          await clearAuth();
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      await clearAuth();
    } finally {
      setLoading(false);
    }
  };

  const login = () => {
    if (Platform.OS === 'web') {
      // Web: Direct browser redirect
      window.location.href = `${MIDDLEWARE_URL}/auth/login?platform=web`;
    } else {
      // Mobile: Will show WebView
      return 'show_webview';
    }
  };

  const handleAuthSuccess = async ({ token: newToken, user: userData }) => {
    try {
      // Store token and user data securely
      await SecureStorage.setItem('auth_token', newToken);
      await SecureStorage.setItem('user_data', JSON.stringify(userData));

      setToken(newToken);
      setUser(userData);
      setIsAuthenticated(true);

      Alert.alert('Success', 'Successfully authenticated!');
    } catch (error) {
      console.error('Failed to store auth data:', error);
      Alert.alert('Error', 'Failed to complete authentication');
    }
  };

  const handleAuthError = (error) => {
    console.error('Auth error:', error);
    Alert.alert('Authentication Error', error || 'Authentication failed');
  };

  const refreshToken = async () => {
    try {
      if (!token) return false;

      const response = await fetch(`${MIDDLEWARE_URL}/api/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const { token: newToken } = await response.json();
        await SecureStorage.setItem('auth_token', newToken);
        setToken(newToken);
        return true;
      } else {
        // Refresh failed, need to re-authenticate
        await clearAuth();
        return false;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      await clearAuth();
      return false;
    }
  };

  const logout = async () => {
    try {
      if (token) {
        // Call logout endpoint
        await fetch(`${MIDDLEWARE_URL}/api/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      await clearAuth();
    }
  };

  const clearAuth = async () => {
    await SecureStorage.removeItem('auth_token');
    await SecureStorage.removeItem('user_data');
    setToken(null);
    setUser(null);
    setIsAuthenticated(false);
  };

  return {
    isAuthenticated,
    user,
    token,
    loading,
    login,
    logout,
    refreshToken,
    handleAuthSuccess,
    handleAuthError,
  };
};

// Main App Component Example
const App = () => {
  const [showAuthWebView, setShowAuthWebView] = useState(false);
  const {
    isAuthenticated,
    user,
    loading,
    login,
    logout,
    handleAuthSuccess,
    handleAuthError,
  } = useAuth();

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Loading...</Text>
      </View>
    );
  }

  if (showAuthWebView && Platform.OS !== 'web') {
    return (
      <View style={{ flex: 1 }}>
        <AuthWebView
          onAuthSuccess={(authData) => {
            handleAuthSuccess(authData);
            setShowAuthWebView(false);
          }}
          onAuthError={(error) => {
            handleAuthError(error);
            setShowAuthWebView(false);
          }}
          onCancel={() => setShowAuthWebView(false)}
        />
        <Button
          title="Cancel"
          onPress={() => setShowAuthWebView(false)}
        />
      </View>
    );
  }

  if (!isAuthenticated) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ fontSize: 18, marginBottom: 20 }}>
          Welcome! Please sign in with SSO.
        </Text>
        <Button
          title="Login with SSO"
          onPress={() => {
            const result = login();
            if (result === 'show_webview') {
              setShowAuthWebView(true);
            }
          }}
        />
      </View>
    );
  }

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: 18, marginBottom: 10 }}>
        Welcome, {user?.name}!
      </Text>
      <Text style={{ marginBottom: 20 }}>
        Email: {user?.email}
      </Text>
      <Button title="Logout" onPress={logout} />
    </View>
  );
};

export default App;
