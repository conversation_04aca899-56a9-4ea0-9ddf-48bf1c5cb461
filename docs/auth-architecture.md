# React Native + Laravel Middleware + Okta SSO Architecture

## Overview

This document outlines the authentication architecture for a React Native application (iOS, Android, and Web) using Laravel middleware to handle Okta SSO authentication.

## Architecture Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  React Native   │    │     Laravel      │    │      Okta       │    │    Una CMS      │
│   iOS/Android   │    │   Middleware     │    │      SSO        │    │   Backend       │
│      + Web      │    │                  │    │                 │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │                        │
         │                        │                        │                        │
         ▼                        ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Mobile: WebView │    │  Auth Routes     │    │  OAuth 2.0      │    │   API           │
│ Web: Redirect   │    │  Session Mgmt    │    │  OIDC           │    │   Endpoints     │
│ Storage: Secure │    │  Token Storage   │    │  User Profile   │    │   Data          │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └─────────────────┘
```

## Authentication Flow

### 1. Initial Login Request
```
React Native App → Laravel Middleware
middleware_url = https://your-middleware.com (prod); http://localhost:8000 (dev)
GET {middleware_url}/auth/login
```

### 2. Okta Redirect
```
Laravel Middleware → Okta SSO
Redirect to: https://your-domain.okta.com/oauth2/v1/authorize
Parameters:
- client_id
- response_type=code
- scope=openid profile email
- redirect_uri=https://your-middleware.com/sso-auth/callback // TODO: name 'sso' (middleware <-> okta) routes vs 'auth' (app <-> middleware)
- state=random_secure_string
- code_challenge (PKCE)
```

### 3. User Authentication
```
User authenticates with Okta
Okta → Laravel Middleware
GET /auth/callback?code=xxx&state=xxx
```

### 4. Token Exchange
```
Laravel Middleware → Okta
POST /oauth2/v1/token
- Exchange authorization code for access token
- Retrieve user profile information
```

### 5. Session Creation & App Redirect
```php
// Laravel AuthController@callback
public function callback(Request $request) {
    // 1. Exchange Okta code for tokens
    $oktaTokens = $this->exchangeCodeForTokens($request->code);

    // 2. Get user profile from Okta
    $oktaUser = $this->getOktaUserProfile($oktaTokens['access_token']);

    // 3. Store Okta tokens securely (server-side only)
    $this->storeOktaTokens($oktaUser['sub'], $oktaTokens);

    // 4. Generate YOUR app token for React Native
    $appToken = $this->generateAppToken($oktaUser);

    // 5. Redirect to app with YOUR token
    return redirect("myapp://auth/success?token={$appToken}&user=" . urlencode(json_encode($userInfo)));
}

private function generateAppToken($oktaUser) {
    // Use Laravel Sanctum or JWT
    $user = $this->findOrCreateUser($oktaUser);

    // Option A: Laravel Sanctum (Recommended)
    return $user->createToken('mobile-app')->plainTextToken;

    // Option B: JWT (if you prefer)
    // return JWT::encode(['user_id' => $user->id, 'exp' => time() + 3600], config('app.key'));
}
```

**Token Strategy: Generate Your Own App Tokens**

✅ **Why Not Use Okta Tokens Directly?**
- **Security Isolation** - Okta tokens shouldn't leave your secure middleware
- **Token Scope** - Okta tokens are for Okta APIs, not your middleware APIs
- **Lifecycle Control** - You need independent control over app sessions
- **Okta Compliance** - This actually helps maintain Okta security standards

## Token Architecture

### **App ↔ Middleware Authentication Strategy**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  React Native   │    │     Laravel      │    │      Okta       │
│      App        │    │   Middleware     │    │      SSO        │
│                 │    │                  │    │                 │
│  App Token      │◄──►│  Okta Tokens     │◄──►│  User Profile   │
│  (Your Token)   │    │  (Server-side)   │    │  (OAuth)        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### **Token Storage Strategy**

```php
// What gets stored where:

// 1. SERVER-SIDE (Laravel Database) - Encrypted
user_sessions table:
- user_id
- okta_access_token (encrypted)
- okta_refresh_token (encrypted)
- okta_expires_at
- app_token_hash
- platform (mobile/web)
- created_at

// 2. CLIENT-SIDE Storage (Platform-specific)

// Mobile (iOS/Android) - expo-secure-store
SecureStore.setItemAsync('auth_token', appToken);
SecureStore.setItemAsync('user_data', JSON.stringify(userInfo));

// Web - localStorage (or sessionStorage for more security)
localStorage.setItem('auth_token', appToken);
localStorage.setItem('user_data', JSON.stringify(userInfo));

// Alternative: Web - sessionStorage (cleared on tab close)
sessionStorage.setItem('auth_token', appToken);
sessionStorage.setItem('user_data', JSON.stringify(userInfo));
```

### **Okta Compliance Benefits**

This approach **enhances** Okta security compliance:

#### **1. Token Isolation**
- Okta tokens never leave your secure server
- Reduces attack surface for token theft
- Follows OAuth2 best practices

#### **2. Session Control**
- Your app tokens can have different expiration than Okta
- You can revoke app access without affecting Okta session
- Granular control over API permissions

#### **3. Audit Trail**
```php
// You can log all API calls with your tokens
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/api/user', function (Request $request) {
        // Log API access
        Log::info('API access', [
            'user_id' => $request->user()->id,
            'endpoint' => '/api/user',
            'okta_session_valid' => $this->isOktaSessionValid($request->user())
        ]);

        return $request->user();
    });
});
```

## Implementation Details

### Laravel Middleware Routes

```php
// routes/web.php
Route::prefix('auth')->group(function () {
    Route::get('/login', [AuthController::class, 'login'])->name('auth.login');
    Route::get('/callback', [AuthController::class, 'callback'])->name('auth.callback');
    Route::get('/success', [AuthController::class, 'success'])->name('auth.success');
    Route::post('/logout', [AuthController::class, 'logout'])->name('auth.logout');
    Route::post('/refresh', [AuthController::class, 'refresh'])->name('auth.refresh');
});

// API routes for authenticated requests
Route::middleware('auth:sanctum')->prefix('api')->group(function () {
    Route::get('/user', [AuthController::class, 'user']);
    Route::get('/profile', [UserController::class, 'profile']);
    // Other protected API endpoints
});
```

### Platform-Specific Authentication

#### **Mobile (iOS/Android) - WebView Integration**

```javascript
import { WebView } from 'react-native-webview';
import * as SecureStore from 'expo-secure-store';

const AuthWebView = ({ onAuthSuccess, onAuthError }) => {
  const handleNavigationStateChange = (navState) => {
    const { url } = navState;
    
    // Check for success redirect
    if (url.includes('myapp://auth/success')) {
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const token = urlParams.get('token');
      const userData = JSON.parse(urlParams.get('user') || '{}');
      
      // Store token securely
      SecureStore.setItemAsync('auth_token', token);
      SecureStore.setItemAsync('user_data', JSON.stringify(userData));
      
      onAuthSuccess({ token, user: userData });
    }
    
    // Check for error redirect
    if (url.includes('myapp://auth/error')) {
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const error = urlParams.get('error');
      onAuthError(error);
    }
  };

  return (
    <WebView
      source={{ uri: 'https://your-middleware.com/auth/login' }}
      onNavigationStateChange={handleNavigationStateChange}
      startInLoadingState={true}
      javaScriptEnabled={true}
      domStorageEnabled={true}
    />
  );
};
```

#### **Web - Direct Integration**

For web platforms, you can't use WebViews or custom URL schemes. Instead, implement direct browser-based authentication:

```javascript
// Web-specific authentication flow
const WebAuthFlow = () => {
  const initiateLogin = () => {
    // Direct browser redirect to middleware
    window.location.href = `${middleware_url}/auth/login?platform=web`;
  };

  const handleAuthCallback = () => {
    // Check URL parameters for auth result
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const userData = urlParams.get('user');

    if (token) {
      // Store token in secure web storage
      localStorage.setItem('auth_token', token);
      localStorage.setItem('user_data', userData);

      // Clean URL and redirect to app
      window.history.replaceState({}, document.title, window.location.pathname);
      onAuthSuccess({ token, user: JSON.parse(userData) });
    }
  };

  // Check for auth callback on component mount
  useEffect(() => {
    handleAuthCallback();
  }, []);

  return (
    <button onClick={initiateLogin}>
      Login with SSO
    </button>
  );
};
```

#### **Laravel - Platform Detection**

```php
// AuthController - Handle different platforms
public function login(Request $request) {
    $platform = $request->get('platform', 'mobile');

    // Store platform in session for callback handling
    session(['auth_platform' => $platform]);

    // Build Okta authorization URL
    $authUrl = $this->buildOktaAuthUrl($request);

    return redirect($authUrl);
}

public function callback(Request $request) {
    $platform = session('auth_platform', 'mobile');

    // ... process Okta callback ...

    if ($platform === 'web') {
        // Web: Redirect to app with token in URL
        return redirect()->to("/auth/success?token={$appToken}&user=" . urlencode(json_encode($userInfo)));
    } else {
        // Mobile: Use custom scheme
        return redirect("myapp://auth/success?token={$appToken}&user=" . urlencode(json_encode($userInfo)));
    }
}
```

## Security Considerations

### 🔒 **Critical Security Requirements**

#### 1. **PKCE (Proof Key for Code Exchange)**
- **Required for mobile apps** - Prevents authorization code interception
- Generate `code_verifier` and `code_challenge` in Laravel
- Include in Okta authorization request

#### 2. **State Parameter**
- **CSRF Protection** - Generate random state parameter
- Store in session, validate on callback
- Prevents cross-site request forgery attacks

#### 3. **Secure Token Storage**
```javascript
// ✅ GOOD - Using expo-secure-store
await SecureStore.setItemAsync('auth_token', token);

// ❌ BAD - Plain AsyncStorage
await AsyncStorage.setItem('auth_token', token);
```

#### 4. **HTTPS Everywhere**
- All communication must use HTTPS
- Even in development, consider SSL certificates
- Okta requires HTTPS redirect URIs in production

#### 5. **Token Encryption at Rest**
```php
// Laravel - Encrypt tokens before database storage
$encryptedToken = encrypt($accessToken);
DB::table('user_sessions')->insert([
    'user_id' => $user->id,
    'access_token' => $encryptedToken,
    'platform' => $request->get('platform', 'mobile'),
    'expires_at' => now()->addSeconds($expiresIn)
]);
```

#### 6. **Web-Specific Security Considerations**

##### **Storage Security:**
```javascript
// Web storage security levels (in order of security)

// 1. sessionStorage (Most Secure - cleared on tab close)
sessionStorage.setItem('auth_token', token);

// 2. localStorage (Persistent but less secure)
localStorage.setItem('auth_token', token);

// 3. Cookies with security flags (Alternative approach)
document.cookie = `auth_token=${token}; Secure; HttpOnly; SameSite=Strict`;
```

##### **CSRF Protection:**
```php
// Laravel - Web-specific CSRF handling
Route::middleware(['web', 'csrf'])->group(function () {
    Route::get('/auth/login', [AuthController::class, 'login']);
    Route::get('/auth/callback', [AuthController::class, 'callback']);
});
```

##### **Content Security Policy (CSP):**
```php
// Add CSP headers for web security
Route::middleware('csp')->group(function () {
    // Web auth routes
});

// CSP Middleware
class ContentSecurityPolicy {
    public function handle($request, Closure $next) {
        $response = $next($request);

        $response->headers->set('Content-Security-Policy',
            "default-src 'self'; " .
            "script-src 'self' 'unsafe-inline'; " .
            "connect-src 'self' https://your-domain.okta.com; " .
            "frame-ancestors 'none';"
        );

        return $response;
    }
}
```

### 🛡️ **Additional Security Best Practices**

#### **Token Management**
- Store access tokens encrypted in database
- Use short-lived access tokens (15-60 minutes)
- Implement automatic refresh token rotation
- Clear tokens on logout

#### **Session Security**
- Use Laravel's built-in session security
- Set secure session cookies (httpOnly, secure, sameSite)
- Implement session timeout matching Okta settings
- Log security events (login, logout, token refresh)

#### **API Security**
```php
// Use Laravel Sanctum for API authentication
Route::middleware('auth:sanctum')->group(function () {
    // Protected API routes
});
```

## WebView Communication Factors

### ✅ **App Redirect Approach (Recommended)**

**Pros:**
- Simple and reliable
- Works across all platforms
- No complex postMessage handling
- Clear success/error states

**Factors to Consider:**

#### 1. **Custom URL Scheme Registration**
```javascript
// app.json (Expo)
{
  "expo": {
    "scheme": "myapp"
  }
}
```

#### 2. **Deep Link Handling**
```javascript
import * as Linking from 'expo-linking';

// Listen for deep links
Linking.addEventListener('url', handleDeepLink);

const handleDeepLink = (event) => {
  if (event.url.includes('auth/success')) {
    // Handle successful auth
  }
};
```

#### 3. **Fallback Mechanisms**
- Handle cases where custom scheme fails
- Provide manual "Continue" button as backup
- Implement timeout for auth process

#### 4. **Error Handling**
```
Success: myapp://auth/success?token=xxx&user=xxx
Error:   myapp://auth/error?error=access_denied&description=xxx
Cancel:  myapp://auth/cancel
```

## Cross-Platform Authentication Considerations

### **Web-Specific Challenges & Solutions**

#### **🚫 What Doesn't Work on Web:**
- **WebViews** - Web apps run in browsers, not WebViews
- **Custom URL Schemes** - `myapp://` schemes don't work in browsers
- **expo-secure-store** - Not available in web browsers
- **Deep Linking** - Browser-based apps use different navigation

#### **✅ Web Solutions:**

##### **1. Storage Options**
```javascript
// Platform-specific storage utility
const SecureStorage = {
  async setItem(key, value) {
    if (Platform.OS === 'web') {
      // Web: Use localStorage or sessionStorage
      localStorage.setItem(key, value);
      // OR for more security (cleared on tab close):
      // sessionStorage.setItem(key, value);
    } else {
      // Mobile: Use expo-secure-store
      await SecureStore.setItemAsync(key, value);
    }
  },

  async getItem(key) {
    if (Platform.OS === 'web') {
      return localStorage.getItem(key);
      // OR: return sessionStorage.getItem(key);
    } else {
      return await SecureStore.getItemAsync(key);
    }
  }
};
```

##### **2. Authentication Flow**
```javascript
// Universal auth hook
const useAuth = () => {
  const login = () => {
    if (Platform.OS === 'web') {
      // Web: Direct browser redirect
      window.location.href = `${middleware_url}/auth/login?platform=web`;
    } else {
      // Mobile: Open WebView
      setShowAuthWebView(true);
    }
  };

  const handleAuthResult = (token, userData) => {
    // Universal token storage
    SecureStorage.setItem('auth_token', token);
    SecureStorage.setItem('user_data', JSON.stringify(userData));

    // Update app state
    setUser(userData);
    setIsAuthenticated(true);
  };

  return { login, handleAuthResult };
};
```

##### **3. CORS Configuration**
```php
// Laravel - config/cors.php
return [
    'paths' => ['api/*', 'auth/*'],
    'allowed_methods' => ['*'],
    'allowed_origins' => [
        'http://localhost:3000',  // Local web development
        'https://your-web-app.com',  // Production web app
        'exp://*************:19000',  // Expo development
    ],
    'allowed_headers' => ['*'],
    'exposed_headers' => [],
    'max_age' => 0,
    'supports_credentials' => true,
];
```

### **Mobile vs Web Communication Summary**

#### **Mobile (WebView) Approach:**
- **Custom URL schemes** for app communication
- **WebView navigation** detection
- **expo-secure-store** for token storage
- **Deep linking** for auth callbacks

#### **Web (Browser) Approach:**
- **Direct browser redirects** for authentication
- **URL parameters** for auth callbacks
- **localStorage/sessionStorage** for token storage
- **History API** for clean URLs

## Token Refresh Strategy

### **When App Token Expires:**
```javascript
// Universal token refresh (works on mobile and web)
const refreshAuthToken = async () => {
  try {
    const currentToken = await SecureStorage.getItem('auth_token');

    const response = await fetch(`${middleware_url}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const { token } = await response.json();
      await SecureStorage.setItem('auth_token', token);
      return token;
    } else {
      // Token refresh failed - redirect to login
      redirectToLogin();
    }
  } catch (error) {
    redirectToLogin();
  }
};
```

### **Laravel Refresh Endpoint:**
```php
// AuthController@refresh
public function refresh(Request $request) {
    $user = $request->user(); // From your app token

    // Check if Okta session is still valid
    if (!$this->isOktaSessionValid($user)) {
        return response()->json(['error' => 'Okta session expired'], 401);
    }

    // Generate new app token
    $newToken = $user->createToken('mobile-app')->plainTextToken;

    return response()->json(['token' => $newToken]);
}

private function isOktaSessionValid($user) {
    $session = DB::table('user_sessions')
        ->where('user_id', $user->id)
        ->first();

    if (!$session || now()->isAfter($session->okta_expires_at)) {
        return false;
    }

    // Optionally: Validate with Okta introspection endpoint
    return true;
}
```

## Session Management Strategy

### **Okta Account Settings Compliance**

Since you're connecting to an existing Okta SSO account, the middleware must respect:

#### 1. **Session Duration**
- Match Okta's configured session lifetime
- Implement session refresh before expiration
- Handle forced logout from Okta admin

#### 2. **Security Policies**
- Multi-factor authentication requirements
- Password complexity rules
- Account lockout policies
- IP restrictions (if configured)

#### 3. **Token Lifecycle**
```php
// Laravel - Respect Okta token expiration
class TokenManager {
    public function isTokenValid($token) {
        $expiresAt = $this->getTokenExpiration($token);
        return now()->isBefore($expiresAt);
    }
    
    public function refreshTokenIfNeeded($refreshToken) {
        if ($this->shouldRefresh()) {
            return $this->refreshAccessToken($refreshToken);
        }
        return false;
    }
}
```

#### 4. **Logout Handling**
- Implement single logout (SLO) if required
- Clear all middleware sessions
- Revoke tokens with Okta
- Redirect app to logged-out state

## Summary: Token Strategy Benefits

### ✅ **Your App Tokens (Client ↔ Middleware):**
- Secure communication between app ↔ middleware
- Stored in expo-secure-store
- Laravel Sanctum for API authentication
- Independent lifecycle management
- Granular API permissions

### ✅ **Okta Tokens (Server-side only):**
- Validate user sessions with Okta
- Refresh user data when needed
- Maintain SSO compliance
- Never exposed to mobile app
- Encrypted storage in database

### 🔒 **Security Benefits:**
- **Maximum Security** - Okta tokens never leave server
- **Full Okta Compliance** - Follows OAuth2 best practices
- **Simplified React Native** - Only handles your app tokens
- **Audit Trail** - Complete logging of API access
- **Session Control** - Independent token lifecycles

## Next Steps

### **Phase 1: Basic Implementation**
1. Set up Okta application configuration
2. Implement Laravel auth routes
3. Create React Native WebView component
4. Test basic login/logout flow

### **Phase 2: Security Hardening**
1. Implement PKCE flow
2. Add token encryption
3. Set up secure session management
4. Add comprehensive error handling

### **Phase 3: Production Readiness**
1. Security audit and testing
2. Performance optimization
3. Monitoring and logging
4. Documentation and deployment

## Configuration Requirements

### **Okta Application Setup**
- Application type: Web Application
- Grant types: Authorization Code, Refresh Token
- Redirect URIs:
  - `https://your-middleware.com/auth/callback` (production)
  - `http://localhost/auth/callback` (development)
- Logout redirect URIs:
  - `https://your-middleware.com/auth/logout` (production)
  - `http://localhost/auth/logout` (development)
- CORS origins:
  - Your middleware domain
  - Web app domains (if making direct API calls)

### **Laravel Environment**
```env
OKTA_CLIENT_ID=your_client_id
OKTA_CLIENT_SECRET=your_client_secret
OKTA_DOMAIN=your-domain.okta.com
OKTA_REDIRECT_URI=https://your-middleware.com/auth/callback
APP_SCHEME=myapp
```

### **React Native Configuration**

#### **Mobile Configuration:**
```javascript
// Configure deep linking for mobile
const linking = {
  prefixes: ['myapp://'],
  config: {
    screens: {
      AuthSuccess: 'auth/success',
      AuthError: 'auth/error',
    },
  },
};
```

#### **Web Configuration:**
```javascript
// Web-specific configuration
const webConfig = {
  // No custom schemes needed for web
  authSuccessPath: '/auth/success',
  authErrorPath: '/auth/error',

  // Web storage preference
  storageType: 'sessionStorage', // or 'localStorage'

  // CORS settings for API calls
  apiBaseUrl: process.env.NODE_ENV === 'production'
    ? 'https://your-middleware.com'
    : 'http://localhost',
};

// Platform detection utility
const isWeb = Platform.OS === 'web';
const isMobile = Platform.OS === 'ios' || Platform.OS === 'android';
```

#### **Universal App Configuration:**
```javascript
// app.json (Expo)
{
  "expo": {
    "scheme": "myapp",
    "platforms": ["ios", "android", "web"],
    "web": {
      "bundler": "webpack"
    }
  }
}
```
