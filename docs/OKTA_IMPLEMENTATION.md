# Okta SSO Implementation - Phase 1

This document describes the Laravel middleware implementation for Okta SSO authentication as outlined in the [auth-architecture.md](./auth-architecture.md).

## Overview

The implementation provides a secure Laravel middleware that handles Okta OAuth 2.0 authentication flow with PKCE support, token management, and cross-platform compatibility for React Native mobile and web applications.

## Features Implemented

### ✅ Core Authentication Flow
- **Okta OAuth 2.0 with PKCE** - Secure authorization code flow with Proof Key for Code Exchange
- **Token Management** - Encrypted storage of Okta tokens server-side, app tokens for client communication
- **User Management** - Automatic user creation/update from Okta profile data
- **Session Management** - Comprehensive session tracking with platform-specific handling
- **Cross-Platform Support** - Handles both mobile (WebView) and web (direct redirect) authentication

### ✅ Security Features
- **PKCE Implementation** - Prevents authorization code interception attacks
- **State Parameter Validation** - CSRF protection for OAuth flow
- **Token Encryption** - All Okta tokens encrypted at rest using <PERSON><PERSON>'s encryption
- **Secure Token Exchange** - App tokens generated independently from Okta tokens
- **Token Revocation** - Proper cleanup of Okta tokens on logout

### ✅ API Endpoints
- `GET /auth/login` - Initiate Okta authentication
- `GET /auth/callback` - Handle Okta callback
- `GET /api/user` - Get authenticated user data
- `POST /api/refresh` - Refresh app token
- `POST /api/logout` - Logout and revoke tokens

## Database Schema

### Users Table Extensions
```sql
ALTER TABLE users ADD COLUMN okta_user_id VARCHAR(255) UNIQUE;
ALTER TABLE users ADD COLUMN okta_email VARCHAR(255);
ALTER TABLE users ADD COLUMN okta_profile_data JSON;
ALTER TABLE users MODIFY password VARCHAR(255) NULL;
```

### User Sessions Table
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    okta_access_token TEXT,           -- Encrypted
    okta_refresh_token TEXT,          -- Encrypted  
    okta_id_token TEXT,               -- Encrypted
    okta_expires_at TIMESTAMP,
    app_token_hash VARCHAR(255),      -- Hash of app token
    platform VARCHAR(50) DEFAULT 'mobile',
    okta_session_id VARCHAR(255),
    okta_user_data JSON,
    state VARCHAR(255),               -- OAuth state
    code_verifier VARCHAR(255),       -- PKCE verifier
    code_challenge VARCHAR(255),      -- PKCE challenge
    is_active BOOLEAN DEFAULT TRUE,
    last_activity_at TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_platform_active (platform, is_active),
    INDEX idx_okta_expires (okta_expires_at),
    INDEX idx_last_activity (last_activity_at)
);
```

## Configuration

### Environment Variables
```env
# Okta Configuration
OKTA_DOMAIN=your-domain.okta.com
OKTA_CLIENT_ID=your_client_id
OKTA_CLIENT_SECRET=your_client_secret
OKTA_REDIRECT_URI=http://localhost:8000/auth/callback

# App Configuration
APP_SCHEME=myapp
FRONTEND_URL=http://localhost:3000
```

### Okta Application Setup
1. **Application Type**: Web Application
2. **Grant Types**: Authorization Code, Refresh Token
3. **Redirect URIs**:
   - Development: `http://localhost:8000/auth/callback`
   - Production: `https://your-middleware.com/auth/callback`
4. **Logout Redirect URIs**:
   - Development: `http://localhost:8000/auth/logout`
   - Production: `https://your-middleware.com/auth/logout`

## Usage Examples

### Mobile App Integration (React Native)
```javascript
import { WebView } from 'react-native-webview';
import * as SecureStore from 'expo-secure-store';

const AuthWebView = ({ onAuthSuccess, onAuthError }) => {
  const handleNavigationStateChange = (navState) => {
    const { url } = navState;
    
    if (url.includes('myapp://auth/success')) {
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const token = urlParams.get('token');
      const userData = JSON.parse(urlParams.get('user') || '{}');
      
      // Store securely
      SecureStore.setItemAsync('auth_token', token);
      SecureStore.setItemAsync('user_data', JSON.stringify(userData));
      
      onAuthSuccess({ token, user: userData });
    }
  };

  return (
    <WebView
      source={{ uri: 'http://localhost:8000/auth/login' }}
      onNavigationStateChange={handleNavigationStateChange}
    />
  );
};
```

### Web App Integration
```javascript
// Initiate login
const login = () => {
  window.location.href = 'http://localhost:8000/auth/login?platform=web';
};

// Handle callback (on success page)
const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get('token');
const userData = urlParams.get('user');

if (token) {
  localStorage.setItem('auth_token', token);
  localStorage.setItem('user_data', userData);
}
```

### API Usage
```javascript
// Make authenticated requests
const response = await fetch('/api/user', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  },
});

// Refresh token
const refreshResponse = await fetch('/api/refresh', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  },
});
```

## Testing

### Run Tests
```bash
# Run all tests
php artisan test

# Run specific test suites
php artisan test tests/Feature/OktaAuthenticationTest.php
php artisan test tests/Unit/UserSessionTest.php
php artisan test tests/Unit/OktaServiceTest.php
```

### Test Coverage
- **Feature Tests**: Complete authentication flow testing
- **Unit Tests**: Model relationships, encryption, service methods
- **Mocking**: Okta API calls properly mocked for reliable testing

## Security Considerations

### ✅ Implemented Security Measures
1. **PKCE Flow** - Prevents code interception attacks
2. **State Validation** - CSRF protection
3. **Token Encryption** - All sensitive tokens encrypted at rest
4. **Token Isolation** - Okta tokens never exposed to client
5. **Secure Storage** - Uses Laravel's encryption for token storage
6. **CORS Configuration** - Properly configured for cross-platform support

### 🔒 Additional Recommendations
1. **HTTPS Only** - Ensure all communication uses HTTPS in production
2. **Token Rotation** - Implement regular token refresh
3. **Session Timeout** - Match Okta session lifetime settings
4. **Audit Logging** - Log all authentication events
5. **Rate Limiting** - Implement rate limiting on auth endpoints

## Troubleshooting

### Common Issues

1. **Invalid State Parameter**
   - Ensure session storage is working properly
   - Check that state is being stored and retrieved correctly

2. **Token Exchange Failures**
   - Verify Okta client credentials
   - Check redirect URI matches exactly
   - Ensure PKCE parameters are correct

3. **CORS Issues**
   - Update `config/cors.php` with your frontend domains
   - Ensure `supports_credentials` is set to `true`

4. **Encryption Errors**
   - Verify `APP_KEY` is set and valid
   - Check that encryption is working in your environment

## Next Steps

### Phase 2 Enhancements
1. **Token Refresh Automation** - Automatic background token refresh
2. **Single Logout (SLO)** - Implement Okta single logout
3. **Advanced Security** - Add additional security headers and validation
4. **Monitoring** - Add comprehensive logging and monitoring
5. **Performance** - Optimize database queries and caching

### Production Deployment
1. **Environment Setup** - Configure production Okta application
2. **SSL Certificates** - Ensure HTTPS is properly configured
3. **Database Migration** - Run migrations in production
4. **Monitoring Setup** - Configure logging and error tracking
5. **Load Testing** - Test authentication flow under load

## Support

For issues or questions about this implementation, refer to:
- [Architecture Documentation](./auth-architecture.md)
- Laravel Sanctum Documentation
- Okta OAuth 2.0 Documentation
- Test files for usage examples
