# Okta Integration Testing Guide

This guide explains how to test your actual Okta configuration using the integration tests and command-line tools provided.

## Overview

We've created two types of tests to verify your Okta setup:

1. **Integration Tests** - Automated tests that verify your Okta configuration and make real API calls
2. **Command-Line Tool** - Interactive tool for testing and debugging your Okta setup

## Quick Setup Test (Command Line)

The fastest way to verify your Okta configuration:

```bash
# Basic configuration and connectivity test
php artisan okta:test-setup

# Full test including API calls
php artisan okta:test-setup --full
```

### What the Command Tests

**Basic Test (`okta:test-setup`):**
- ✅ Environment variables are set
- ✅ Domain format is correct
- ✅ Domain is reachable
- ✅ PKCE generation works
- ✅ Authorization URL generation works

**Full Test (`okta:test-setup --full`):**
- ✅ All basic tests above
- ✅ Token introspection API works
- ✅ OpenID configuration is accessible

## Integration Tests

### Setup

1. **Configure your Okta credentials** in `.env`:
   ```env
   OKTA_DOMAIN=your-domain.okta.com
   OKTA_CLIENT_ID=your_client_id
   OKTA_CLIENT_SECRET=your_client_secret
   OKTA_REDIRECT_URI=http://localhost:8000/auth/callback
   ```

2. **Enable integration tests** by adding to `.env`:
   ```env
   OKTA_INTEGRATION_TEST=true
   ```

3. **Run the integration tests**:
   ```bash
   # Run only integration tests
   php artisan test --testsuite=Integration
   
   # Run specific integration test
   php artisan test --filter="OktaIntegrationTest"
   ```

### What the Integration Tests Verify

#### ✅ Configuration Validation
- All required environment variables are set
- Domain format is correct (ends with .okta.com)
- No common configuration mistakes

#### ✅ PKCE Implementation
- Code verifier generation (128 characters)
- Code challenge generation (base64url encoded)
- Proper cryptographic randomness

#### ✅ OAuth Flow Structure
- Authorization URL contains all required parameters
- Parameters are properly encoded
- State and PKCE parameters are included

#### ✅ API Connectivity
- Domain is reachable
- Token introspection endpoint works
- OpenID configuration is accessible
- Network connectivity is stable

#### ✅ Database Integration
- User creation from Okta profile data
- UserSession creation and management
- JSON data handling works correctly
- Token encryption/decryption works

#### ✅ Complete Flow Simulation
- End-to-end OAuth flow simulation
- User and session creation
- App token generation
- Session cleanup

## Example Output

### Command Line Tool Output
```bash
$ php artisan okta:test-setup --full

🔍 Testing Okta Configuration...

1. Testing Configuration...
   ✅ Domain: dev-12345.okta.com
   ✅ Client ID: 0oa1b2c3...
   ✅ Client Secret: ********
   ✅ Redirect URI: http://localhost:8000/auth/callback

2. Testing Domain Connectivity...
   ✅ Domain is reachable

3. Testing PKCE Generation...
   ✅ PKCE challenge generated successfully
   📝 Code verifier length: 128
   📝 Code challenge: dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk...

4. Testing Authorization URL Generation...
   ✅ Authorization URL generated successfully
   🔗 URL: https://dev-12345.okta.com/oauth2/v1/authorize?client_id=0oa1b2c3...
   ✅ All required OAuth parameters present

5. Testing Token Introspection (API Call)...
   ✅ Token introspection API working (dummy token correctly rejected)

6. Testing OpenID Configuration...
   ✅ OpenID configuration retrieved successfully
   📝 Issuer: https://dev-12345.okta.com
   📝 Authorization endpoint: https://dev-12345.okta.com/oauth2/v1/authorize
   📝 Token endpoint: https://dev-12345.okta.com/oauth2/v1/token

✅ Okta setup test completed!
```

### Integration Test Output
```bash
$ php artisan test --testsuite=Integration

PASS  Tests\Integration\OktaIntegrationTest
✓ okta service configuration is valid
✓ pkce challenge generation  
✓ authorization url generation
✓ token introspection with invalid token
✓ token revocation with invalid token
✓ oauth flow simulation
✓ okta domain connectivity

Tests:  7 passed (25 assertions)
```

## Troubleshooting

### Common Issues

#### ❌ "Required environment variable OKTA_DOMAIN is not set"
**Solution:** Add your Okta domain to `.env`:
```env
OKTA_DOMAIN=your-domain.okta.com
```

#### ❌ "Domain should not include https://"
**Solution:** Remove the protocol from your domain:
```env
# ❌ Wrong
OKTA_DOMAIN=https://dev-12345.okta.com

# ✅ Correct  
OKTA_DOMAIN=dev-12345.okta.com
```

#### ❌ "Cannot reach domain"
**Possible causes:**
- Network connectivity issues
- Incorrect domain name
- Firewall blocking HTTPS requests

**Solution:** Verify your domain in a browser: `https://your-domain.okta.com`

#### ❌ "Token introspection failed"
**Possible causes:**
- Incorrect client credentials
- Client not configured for introspection
- Network issues

**Solution:** Verify your `OKTA_CLIENT_ID` and `OKTA_CLIENT_SECRET`

### Skipped Tests

If you see "Okta integration tests are disabled", add to your `.env`:
```env
OKTA_INTEGRATION_TEST=true
```

## Security Notes

⚠️ **Important:** Integration tests make real API calls to Okta using your actual credentials. 

- Only run these tests in development/staging environments
- Never commit real Okta credentials to version control
- Use environment-specific `.env` files
- Consider using separate Okta applications for testing

## Next Steps

Once your integration tests pass:

1. ✅ **Your Okta configuration is correct**
2. ✅ **API connectivity is working**  
3. ✅ **OAuth flow will work in your application**
4. ✅ **Ready to test the full authentication flow**

You can now confidently:
- Test the web authentication flow at `/auth/login`
- Integrate with your React Native application
- Deploy to staging/production environments

## Advanced Testing

For more comprehensive testing, you can:

1. **Test with real user accounts** (add `OKTA_TEST_USERNAME` to `.env`)
2. **Test token refresh flows** (requires valid refresh tokens)
3. **Test different OAuth scopes** (modify the OktaService)
4. **Test error scenarios** (network failures, invalid responses)

The integration test framework is designed to be extensible - you can add more specific tests for your use case.
