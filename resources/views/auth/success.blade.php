<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Successful</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }
        .success-icon {
            width: 64px;
            height: 64px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        .success-icon svg {
            width: 32px;
            height: 32px;
            color: white;
        }
        h1 {
            color: #1f2937;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }
        p {
            color: #6b7280;
            margin-bottom: 1.5rem;
        }
        .token-info {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
            word-break: break-all;
        }
        .close-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s;
        }
        .close-btn:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        </div>
        
        <h1>Authentication Successful!</h1>
        <p>You have been successfully authenticated with Okta SSO.</p>
        
        @if($token)
            <div class="token-info">
                <strong>Token:</strong><br>
                {{ Str::limit($token, 50) }}...
            </div>
        @endif
        
        @if($user)
            <div class="token-info">
                <strong>User Data:</strong><br>
                {{ $user }}
            </div>
        @endif
        
        <button class="close-btn" onclick="window.close()">Close Window</button>
        
        <script>
            // Auto-close after 5 seconds if this is a popup
            if (window.opener) {
                setTimeout(() => {
                    window.close();
                }, 5000);
            }
            
            // For web apps, you might want to redirect or post a message
            if (window.parent !== window) {
                // This is in an iframe
                window.parent.postMessage({
                    type: 'AUTH_SUCCESS',
                    token: '{{ $token }}',
                    user: {!! $user !!}
                }, '*');
            }
        </script>
    </div>
</body>
</html>
