#!/bin/bash

COMPOSE_FILE="docker-compose.dev.yaml"
SERVICE="workspace"

case "$1" in
    start)
        clear
        echo "Starting ⌛"
        docker compose -f $COMPOSE_FILE up -d
        echo "Started ✅"
        ;;
    stop)
        clear
        echo "Stopping ⌛"
        docker compose -f $COMPOSE_FILE down
        echo "Stopped ✅"
        ;;
    install)
        docker compose -f $COMPOSE_FILE exec $SERVICE bash -c "composer install && export NVM_DIR=\"\$HOME/.nvm\" && [ -s \"\$NVM_DIR/nvm.sh\" ] && . \"\$NVM_DIR/nvm.sh\" && npm install && npm run dev"
        ;;
    migrate)
        docker compose -f $COMPOSE_FILE exec $SERVICE php artisan migrate
        ;;
    exec)
        docker compose -f $COMPOSE_FILE exec $SERVICE bash
        ;;
    -i)
        echo "Available commands:"
        echo "  start    - Start the containers"
        echo "  stop     - Stop the containers"
        echo "  install  - Run composer and npm install inside the workspace"
        echo "  migrate  - Run PHP artisan migrate inside the workspace"
        echo "  exec     - Open a bash shell inside the workspace"
        echo "  clean    - Stop and remove all containers, networks, volumes, and images"
        echo "  man      - Show this help message"
        ;;
    clean)
      echo "Stopping and removing all containers, networks, volumes, and images defined in $COMPOSE_FILE ⌛"
        docker compose -f $COMPOSE_FILE down --volumes --remove-orphans
        docker volume prune -f
        echo "Deleted ✅"
        ;;
    *)
        echo "Usage: $1 {start|install|migrate|exec|delete}"
        exit 1
        ;;
esac