<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\PostsController;


// DEV AUTHENTICATION (temporary)
Route::post('/dev-login', [AuthController::class, 'devLogin']);

Route::middleware('auth:sanctum')->group(function () {
    // Secured API endpoints
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/refresh', [AuthController::class, 'refresh']);

    //POSTS
    Route::post('/post', [PostsController::class, 'store']);
    Route::get('/post', [PostsController::class, 'index']);
});

