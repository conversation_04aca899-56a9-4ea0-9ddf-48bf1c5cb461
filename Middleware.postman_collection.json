{"info": {"_postman_id": "f04968f3-6daa-41ad-80c0-594f00a93386", "name": "Middleware", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "36646949", "_collection_link": "https://bart33-6202.postman.co/workspace/Una~758665ac-8be0-4004-842d-794e38081ae0/collection/36646949-f04968f3-6daa-41ad-80c0-594f00a93386?action=share&source=collection_link&creator=36646949"}, "item": [{"name": "auth", "item": [{"name": "<PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.all.keys('message', 'session');", "});", "", "pm.test(\"Message must be a non-empty string\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.message).to.exist.and.to.be.a('string').and.to.have.lengthOf.at.least(1, \"Value should not be empty\");", "});", "", "pm.test(\"Session must be a non-empty string\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.session).to.exist.and.to.be.a('string').and.to.have.lengthOf.at.least(1, \"Session should not be empty\");", "    ", "    pm.environment.set(\"middleware-api-token\", responseData.session);", "});", "", "pm.test(\"Response time is less than 200ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(200);", "});", "", "pm.test(\"Response Content-Type is application/json\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include(\"application/json\");", "});", "", "pm.test(\"Response body structure is valid JSON\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object').that.includes.keys('message', 'session');", "});", "", "pm.test(\"Message is a valid string without special characters\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.message).to.exist.and.to.be.a('string').that.matches(/^[a-zA-Z0-9\\s]*$/, \"Message should not contain special characters\");", "});", "", "pm.test(\"Session is a valid session identifier\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData.session).to.exist;", "});", "", "pm.test(\"Response body does not contain any unexpected fields\", function () {", "    const responseData = pm.response.json();", "    ", "    pm.expect(responseData).to.be.an('object');", "    pm.expect(responseData).to.have.all.keys('message', 'session');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "http://localhost/api/dev-login", "protocol": "http", "host": ["localhost"], "path": ["api", "dev-login"]}}, "response": []}, {"name": "logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{middleware-api-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "{{middleware-api-token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost/api/logout", "protocol": "http", "host": ["localhost"], "path": ["api", "logout"]}}, "response": []}, {"name": "user", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{middleware-api-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost/api/user", "protocol": "http", "host": ["localhost"], "path": ["api", "user"]}}, "response": []}]}, {"name": "posts", "item": [{"name": "post", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{middleware-api-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost/api/post", "protocol": "http", "host": ["localhost"], "path": ["api", "post"]}}, "response": []}, {"name": "post Copy", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{middleware-api-token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "text", "value": "hello world", "type": "text"}]}, "url": {"raw": "http://localhost/api/post", "protocol": "http", "host": ["localhost"], "path": ["api", "post"]}}, "response": []}]}]}