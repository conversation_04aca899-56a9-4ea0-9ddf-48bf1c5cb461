<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('okta_access_token')->nullable();
            $table->text('okta_refresh_token')->nullable();
            $table->text('okta_id_token')->nullable();
            $table->timestamp('okta_expires_at')->nullable();
            $table->string('app_token_hash')->nullable();
            $table->string('platform')->default('mobile');
            $table->string('okta_session_id')->nullable();
            $table->text('okta_user_data')->nullable();
            $table->string('state')->nullable();
            $table->string('code_verifier')->nullable();
            $table->string('code_challenge')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'is_active']);
            $table->index(['platform', 'is_active']);
            $table->index('okta_expires_at');
            $table->index('last_activity_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_sessions');
    }
};
