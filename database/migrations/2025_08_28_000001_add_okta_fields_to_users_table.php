<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('name')->nullable()->after('id');
            $table->string('password')->nullable()->after('email_verified_at');
            $table->string('okta_user_id')->nullable()->unique()->after('password');
            $table->string('okta_email')->nullable()->after('okta_user_id');
            $table->text('okta_profile_data')->nullable()->after('okta_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['name', 'password', 'okta_user_id', 'okta_email', 'okta_profile_data']);
        });
    }
};
