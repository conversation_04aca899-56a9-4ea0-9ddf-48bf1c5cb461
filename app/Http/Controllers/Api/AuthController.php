<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSession;
use App\Services\OktaService;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
class AuthController extends Controller
{
    private OktaService $oktaService;

    public function __construct(OktaService $oktaService)
    {
        $this->oktaService = $oktaService;
    }
    ///Unsafe dev only purposes thing.
    public function devLogin(Request $request)
    {
        $email = $request->input('email');

        $user = \App\Models\User::where('email', $email)->first();

        if (!$user) {
            $user = $this->createUser($email);
        }
        if ($user != null) {
            auth()->login($user);
            $sessionId = $user->createToken('api_token')->plainTextToken;

            return response()->json([
                'message' => 'Logged in as user',
                'session' => $sessionId
            ]);
        } else {
            return response()->json(['error' => 'Unable to login or create user'], 500);
        }

    }

    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();
        return response()->json(['message' => 'Logged out']);
    }

    public function user(Request $request)
    {
        return response()->json($request->user());
    }

    /**
     * App-to-Middleware: Initiate Okta SSO login flow.
     * This is the endpoint that React Native apps call to start authentication.
     */
    public function login(Request $request): \Illuminate\Http\RedirectResponse
    {
        $platform = $request->get('platform', 'mobile');

        // Store platform in session for callback handling
        session(['auth_platform' => $platform]);

        // Generate state and PKCE challenge
        /*
        1. Middleware generates random state → Stores it (likely in session or database)
        2. Middleware redirects to Okta → Includes state in the authorization URL
        3. Okta redirects back to middleware → Returns the same state value
        4. Middleware validates → Confirms the returned state matches what was sent
        */
        $state = Str::random(40);
        $pkce = $this->oktaService->generatePkceChallenge();

        // Store state and PKCE in session for validation
        session([
            'oauth_state' => $state,
            'code_verifier' => $pkce['code_verifier'],
        ]);

        // Build authorization URL (this will redirect to Okta, which will callback to $APP_SCHEME://$APP_AUTH_CALLBACK_PATH)
        $authUrl = $this->oktaService->buildAuthorizationUrl($state, $pkce);

        Log::info('App-to-Middleware: Okta login initiated', [
            'platform' => $platform,
            'state' => $state,
        ]);

        return redirect($authUrl);
    }

    /**
     * Middleware-to-SSO: Handle Okta callback after user authentication.
     * This processes the authorization code and redirects to the app deep link.
     */
    public function callback(Request $request): \Illuminate\Http\RedirectResponse
    {
        Log::info('=== OKTA CALLBACK START ===', [
            'query_params' => $request->query(),
            'session_state' => session('oauth_state'),
            'session_verifier_present' => !empty(session('code_verifier')),
            'user_agent' => $request->header('User-Agent'),
        ]);

        // Validate state parameter
        if ($request->query('state') !== session('oauth_state')) {
            Log::error('Invalid state parameter', [
                'received' => $request->query('state'),
                'expected' => session('oauth_state')
            ]);
            return $this->handleCallbackError('Invalid state');
        }

        // Get authorization code
        $code = $request->query('code');
        if (!$code) {
            Log::error('Missing authorization code');
            return $this->handleCallbackError('Missing code');
        }

        Log::info('State validation passed', [
            'state' => $request->query('state'),
            'code_length' => strlen($code),
        ]);

        try {
            // Get Okta issuer URL
            $issuer = config('services.okta.issuer');
            if (!$issuer) {
                Log::error('OKTA_ISSUER not configured', [
                    'issuer' => $issuer,
                    'config' => config('services.okta')
                ]);
                return $this->handleCallbackError('Okta configuration missing');
            }

            $tokenUrl = $issuer . '/v1/token';
            Log::info('=== TOKEN EXCHANGE START ===', [
                'url' => $tokenUrl,
                'grant_type' => 'authorization_code',
                'redirect_uri' => config('services.okta.redirect_uri'),
                'client_id' => config('services.okta.client_id'),
                'code_verifier_present' => !empty(session('code_verifier')),
                'code_length' => strlen($code),
            ]);

            // Exchange code for tokens using direct HTTP call
            $tokenResponse = Http::asForm()->post($tokenUrl, [
                'grant_type' => 'authorization_code',
                'code' => $code,
                'redirect_uri' => config('services.okta.redirect_uri'),
                'client_id' => config('services.okta.client_id'),
                'client_secret' => config('services.okta.client_secret'),
                'code_verifier' => session('code_verifier'),
            ])->throw()->json();

            Log::info('=== TOKEN EXCHANGE SUCCESS ===', [
                'access_token_present' => !empty($tokenResponse['access_token']),
                'id_token_present' => !empty($tokenResponse['id_token']),
                'refresh_token_present' => !empty($tokenResponse['refresh_token']),
                'expires_in' => $tokenResponse['expires_in'] ?? null,
                'token_type' => $tokenResponse['token_type'] ?? null,
            ]);

            // Fetch user profile
            $userinfoUrl = $issuer . '/v1/userinfo';
            Log::info('=== USER INFO FETCH START ===', [
                'url' => $userinfoUrl,
                'access_token_length' => strlen($tokenResponse['access_token']),
            ]);

            $userinfo = Http::withToken($tokenResponse['access_token'])
                ->get($userinfoUrl)->json();

            Log::info('=== USER INFO FETCH SUCCESS ===', [
                'userinfo_keys' => array_keys($userinfo),
                'sub' => $userinfo['sub'] ?? 'missing',
                'email' => $userinfo['email'] ?? 'missing',
                'name' => $userinfo['name'] ?? 'missing',
                'preferred_username' => $userinfo['preferred_username'] ?? 'missing',
            ]);

            $user = [
                'sub' => $userinfo['sub'] ?? null,
                'email' => $userinfo['email'] ?? null,
                'name' => $userinfo['name'] ?? null,
            ];

            // Create or update user in database
            $dbUser = User::createOrUpdateFromOkta($userinfo);

            // Create user session with encrypted Okta tokens
            $userSession = $this->createUserSession($dbUser, $tokenResponse, $userinfo, $request);

            // Clear session data
            session()->forget(['oauth_state', 'code_verifier', 'auth_platform']);

            Log::info('=== JWT TOKEN CREATION START ===', [
                'user_data' => $user,
                'token_signing_key_present' => !empty(config('app.token_signing_key')),
            ]);

            // Create short-lived JWT token for deep link (60 seconds expiry)
            $shortLivedToken = $this->createShortLivedToken($user);

            Log::info('=== JWT TOKEN CREATED ===', [
                'token_length' => strlen($shortLivedToken),
                'token_preview' => substr($shortLivedToken, 0, 50) . '...',
            ]);

            // Build deep link URL
            $deepLinkUrl = $this->buildDeepLinkUrl($shortLivedToken, $user);

            Log::info('=== DEEP LINK REDIRECT ===', [
                'user_sub' => $user['sub'],
                'user_email' => $user['email'],
                'deep_link_url' => $deepLinkUrl,
                'deep_link_length' => strlen($deepLinkUrl),
            ]);

            // Redirect to deep link
            return redirect()->away($deepLinkUrl);

        } catch (\Exception $e) {
            Log::error('Okta callback failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return $this->handleCallbackError($e->getMessage());
        }
    }

    /**
     * App-to-Middleware: Display success page for web platform.
     * This shows the result after successful authentication.
     */
    public function success(Request $request)
    {
        $token = $request->get('token');
        $user = $request->get('user');

        return view('auth.success', compact('token', 'user'));
    }

    /**
     * App-to-Middleware: Display error page for web platform.
     * This shows the result after failed authentication.
     */
    public function error(Request $request)
    {
        $error = $request->get('error', 'Authentication failed');

        return view('auth.error', compact('error'));
    }

    /**
     * App-to-Middleware: Exchange short-lived JWT for Laravel Sanctum API token.
     * This allows the app to convert the deep link JWT into a usable API token.
     */
    public function exchangeToken(Request $request): JsonResponse
    {
        $jwtToken = $request->input('token');

        if (!$jwtToken) {
            return response()->json(['error' => 'Token is required'], 400);
        }

        try {
            // Decode and validate JWT
            $key = base64_decode(config('app.token_signing_key'));
            $decoded = JWT::decode($jwtToken, new Key($key, 'HS256'));

            Log::info('JWT token exchange attempt', [
                'sub' => $decoded->sub ?? 'missing',
                'email' => $decoded->email ?? 'missing',
                'exp' => $decoded->exp ?? 'missing',
                'current_time' => time(),
            ]);

            // Check if token is expired (additional safety check)
            if (isset($decoded->exp) && $decoded->exp < time()) {
                Log::warning('Expired JWT token exchange attempt', [
                    'exp' => $decoded->exp,
                    'current_time' => time(),
                ]);
                return response()->json(['error' => 'Token expired'], 401);
            }

            // Find user by Okta sub
            $user = User::where('okta_user_id', $decoded->sub)->first();

            if (!$user) {
                Log::error('User not found for JWT token exchange', [
                    'okta_user_id' => $decoded->sub,
                ]);
                return response()->json(['error' => 'User not found'], 404);
            }

            // Create Sanctum token (same structure as devLogin)
            $sessionId = $user->createToken('api_token')->plainTextToken;

            Log::info('JWT token exchange successful', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'token_length' => strlen($sessionId),
            ]);

            return response()->json([
                'message' => 'Token exchanged successfully',
                'session' => $sessionId  // Same structure as devLogin
            ]);

        } catch (\Firebase\JWT\ExpiredException $e) {
            Log::warning('Expired JWT token in exchange', [
                'error' => $e->getMessage(),
            ]);
            return response()->json(['error' => 'Token expired'], 401);
        } catch (\Firebase\JWT\SignatureInvalidException $e) {
            Log::error('Invalid JWT signature in exchange', [
                'error' => $e->getMessage(),
            ]);
            return response()->json(['error' => 'Invalid token signature'], 401);
        } catch (\Exception $e) {
            Log::error('JWT token exchange failed', [
                'error' => $e->getMessage(),
                'token_preview' => substr($jwtToken, 0, 50) . '...',
            ]);
            return response()->json(['error' => 'Invalid token'], 401);
        }
    }

    /**
     * App-to-Middleware: Refresh app token if Okta session is still valid.
     * This allows apps to refresh their tokens without re-authentication.
     */
    public function refresh(Request $request): JsonResponse
    {
        $user = $request->user(); // From your app token

        // Check if Okta session is still valid
        if (!$this->isOktaSessionValid($user)) {
            return response()->json(['error' => 'Okta session expired'], 401);
        }

        // Generate new app token
        $newToken = $user->createToken('mobile-app')->plainTextToken;

        return response()->json(['token' => $newToken]);
    }

    /**
     * Create user session with encrypted Okta tokens.
     */
    private function createUserSession(User $user, array $tokens, array $oktaProfile, Request $request): UserSession
    {
        return UserSession::create([
            'user_id' => $user->id,
            'okta_access_token' => $tokens['access_token'],
            'okta_refresh_token' => $tokens['refresh_token'] ?? null,
            'okta_id_token' => $tokens['id_token'] ?? null,
            'okta_expires_at' => now()->addSeconds($tokens['expires_in'] ?? 3600),
            'platform' => session('auth_platform', 'mobile'),
            'okta_user_data' => $oktaProfile,
            'is_active' => true,
            'last_activity_at' => now(),
        ]);
    }

    /**
     * Check if Okta session is still valid.
     */
    private function isOktaSessionValid(User $user): bool
    {
        $session = UserSession::where('user_id', $user->id)
            ->where('is_active', true)
            ->latest()
            ->first();

        if (!$session || !$session->isOktaSessionValid()) {
            return false;
        }

        // Update activity timestamp
        $session->updateActivity();

        return true;
    }

    private function createUser($email)
    {
        return \App\Models\User::create([
            'email' => $email,
            'cookie-session' => '', // default password
        ]);
    }

    /**
     * Detect if the request is coming from a WebView (mobile app).
     */
    private function isWebViewRequest(string $userAgent): bool
    {
        // Common WebView user agent patterns
        $webViewPatterns = [
            'Mobile/15E148',           // iOS WebView
            'wv',                      // Android WebView
            'WebView',                 // Generic WebView
            'ReactNative',             // React Native
            'Expo',                    // Expo
        ];

        foreach ($webViewPatterns as $pattern) {
            if (stripos($userAgent, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create a short-lived JWT token for deep link authentication.
     */
    private function createShortLivedToken(array $user): string
    {
        $now = time();
        $payload = [
            'sub' => $user['sub'],
            'email' => $user['email'],
            'name' => $user['name'],
            'exp' => $now + 60, // 60 seconds expiry
            'iat' => $now,
        ];

        Log::info('JWT payload created', [
            'payload' => $payload,
            'expires_at' => date('Y-m-d H:i:s', $payload['exp']),
            'issued_at' => date('Y-m-d H:i:s', $payload['iat']),
        ]);

        $key = base64_decode(config('app.token_signing_key'));
        $token = JWT::encode($payload, $key, 'HS256');

        Log::info('JWT token encoded', [
            'key_length' => strlen($key),
            'algorithm' => 'HS256',
        ]);

        return $token;
    }

    /**
     * Build the deep link URL with token and user data.
     */
    private function buildDeepLinkUrl(string $token, array $user): string
    {
        $userJson = urlencode(json_encode($user));
        $queryParams = 'token=' . urlencode($token) . '&user=' . $userJson;

        $scheme = config('app.scheme');
        $path = config('app.auth_callback_path');
        $baseUrl = $scheme . '://' . $path;

        return $baseUrl . '?' . $queryParams;
    }

    /**
     * Handle callback errors by redirecting to deep link with error.
     */
    private function handleCallbackError(string $error): \Illuminate\Http\RedirectResponse
    {
        // Clear session data
        session()->forget(['oauth_state', 'code_verifier', 'auth_platform']);

        $queryParams = 'error=' . urlencode($error);

        $scheme = config('app.scheme');
        $path = config('app.auth_callback_path');
        $baseUrl = $scheme . '://' . $path;
        $errorUrl = $baseUrl . '?' . $queryParams;

        Log::info('Redirecting to deep link with error', [
            'error' => $error,
            'error_url' => $errorUrl,
        ]);

        return redirect()->away($errorUrl);
    }
}
