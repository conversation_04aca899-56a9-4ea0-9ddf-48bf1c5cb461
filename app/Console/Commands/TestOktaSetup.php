<?php

namespace App\Console\Commands;

use App\Services\OktaService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestOktaSetup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'okta:test-setup {--full : Run full connectivity tests}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Okta configuration and connectivity';

    private OktaService $oktaService;

    public function __construct(OktaService $oktaService)
    {
        parent::__construct();
        $this->oktaService = $oktaService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing Okta Configuration...');
        $this->newLine();

        // Test 1: Configuration validation
        $this->testConfiguration();

        // Test 2: Domain connectivity
        $this->testDomainConnectivity();

        // Test 3: PKCE generation
        $this->testPkceGeneration();

        // Test 4: Authorization URL generation
        $this->testAuthorizationUrl();

        if ($this->option('full')) {
            // Test 5: Token introspection (makes actual API call)
            $this->testTokenIntrospection();

            // Test 6: OpenID configuration
            $this->testOpenIdConfiguration();
        }

        $this->newLine();
        $this->info('✅ Okta setup test completed!');
        
        if (!$this->option('full')) {
            $this->comment('💡 Run with --full flag to test actual API connectivity');
        }
    }

    private function testConfiguration(): void
    {
        $this->info('1. Testing Configuration...');

        $domain = config('services.okta.domain');
        $clientId = config('services.okta.client_id');
        $clientSecret = config('services.okta.client_secret');
        $redirectUri = config('services.okta.redirect_uri');

        if (empty($domain)) {
            $this->error('   ❌ OKTA_DOMAIN is not set');
            return;
        }
        $this->line("   ✅ Domain: {$domain}");

        if (empty($clientId)) {
            $this->error('   ❌ OKTA_CLIENT_ID is not set');
            return;
        }
        $this->line("   ✅ Client ID: " . substr($clientId, 0, 8) . '...');

        if (empty($clientSecret)) {
            $this->error('   ❌ OKTA_CLIENT_SECRET is not set');
            return;
        }
        $this->line("   ✅ Client Secret: " . str_repeat('*', strlen($clientSecret)));

        if (empty($redirectUri)) {
            $this->error('   ❌ OKTA_REDIRECT_URI is not set');
            return;
        }
        $this->line("   ✅ Redirect URI: {$redirectUri}");

        // Validate domain format
        if (!str_contains($domain, '.okta.com')) {
            $this->warn('   ⚠️  Domain should end with .okta.com');
        }

        if (str_contains($domain, 'https://')) {
            $this->warn('   ⚠️  Domain should not include https://');
        }

        $this->newLine();
    }

    private function testDomainConnectivity(): void
    {
        $this->info('2. Testing Domain Connectivity...');

        $domain = config('services.okta.domain');
        if (empty($domain)) {
            $this->error('   ❌ Cannot test connectivity - domain not configured');
            $this->newLine();
            return;
        }

        try {
            $response = Http::timeout(10)->get("https://{$domain}");
            
            if ($response->successful()) {
                $this->line('   ✅ Domain is reachable');
            } else {
                $this->error("   ❌ Domain returned status: {$response->status()}");
            }
        } catch (\Exception $e) {
            $this->error("   ❌ Cannot reach domain: {$e->getMessage()}");
        }

        $this->newLine();
    }

    private function testPkceGeneration(): void
    {
        $this->info('3. Testing PKCE Generation...');

        try {
            $pkce = $this->oktaService->generatePkceChallenge();

            if (isset($pkce['code_verifier']) && isset($pkce['code_challenge'])) {
                $this->line('   ✅ PKCE challenge generated successfully');
                $this->line("   📝 Code verifier length: " . strlen($pkce['code_verifier']));
                $this->line("   📝 Code challenge: " . substr($pkce['code_challenge'], 0, 20) . '...');
            } else {
                $this->error('   ❌ PKCE challenge missing required fields');
            }
        } catch (\Exception $e) {
            $this->error("   ❌ PKCE generation failed: {$e->getMessage()}");
        }

        $this->newLine();
    }

    private function testAuthorizationUrl(): void
    {
        $this->info('4. Testing Authorization URL Generation...');

        try {
            $pkce = $this->oktaService->generatePkceChallenge();
            $state = 'test_state_' . time();
            $authUrl = $this->oktaService->buildAuthorizationUrl($state, $pkce);

            if (!empty($authUrl) && str_starts_with($authUrl, 'https://')) {
                $this->line('   ✅ Authorization URL generated successfully');
                $this->line("   🔗 URL: " . substr($authUrl, 0, 80) . '...');
                
                // Parse and validate URL parameters
                $urlParts = parse_url($authUrl);
                if (isset($urlParts['query'])) {
                    parse_str($urlParts['query'], $params);
                    
                    $requiredParams = ['client_id', 'response_type', 'scope', 'redirect_uri', 'state', 'code_challenge', 'code_challenge_method'];
                    $missingParams = array_diff($requiredParams, array_keys($params));
                    
                    if (empty($missingParams)) {
                        $this->line('   ✅ All required OAuth parameters present');
                    } else {
                        $this->warn('   ⚠️  Missing parameters: ' . implode(', ', $missingParams));
                    }
                }
            } else {
                $this->error('   ❌ Invalid authorization URL generated');
            }
        } catch (\Exception $e) {
            $this->error("   ❌ Authorization URL generation failed: {$e->getMessage()}");
        }

        $this->newLine();
    }

    private function testTokenIntrospection(): void
    {
        $this->info('5. Testing Token Introspection (API Call)...');

        try {
            // Test with a dummy token - should return active: false
            $result = $this->oktaService->introspectToken('dummy_token_12345');

            if (is_array($result) && isset($result['active'])) {
                if ($result['active'] === false) {
                    $this->line('   ✅ Token introspection API working (dummy token correctly rejected)');
                } else {
                    $this->warn('   ⚠️  Unexpected response for dummy token');
                }
            } else {
                $this->error('   ❌ Invalid response format from introspection endpoint');
            }
        } catch (\Exception $e) {
            $this->error("   ❌ Token introspection failed: {$e->getMessage()}");
        }

        $this->newLine();
    }

    private function testOpenIdConfiguration(): void
    {
        $this->info('6. Testing OpenID Configuration...');

        $domain = config('services.okta.domain');
        $authServerId = config('services.okta.auth_server_id', 'default');

        try {
            $response = Http::timeout(10)->get("https://{$domain}/oauth2/{$authServerId}/.well-known/openid_configuration");

            if ($response->successful()) {
                $config = $response->json();

                if (isset($config['issuer'], $config['authorization_endpoint'], $config['token_endpoint'])) {
                    $this->line('   ✅ OpenID configuration retrieved successfully');
                    $this->line("   📝 Issuer: {$config['issuer']}");
                    $this->line("   📝 Authorization endpoint: {$config['authorization_endpoint']}");
                    $this->line("   📝 Token endpoint: {$config['token_endpoint']}");
                } else {
                    $this->error('   ❌ OpenID configuration missing required fields');
                }
            } else {
                $this->error("   ❌ OpenID configuration request failed: {$response->status()}");
            }
        } catch (\Exception $e) {
            $this->error("   ❌ OpenID configuration test failed: {$e->getMessage()}");
        }

        $this->newLine();
    }
}
