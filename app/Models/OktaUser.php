<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'okta_user_id',
        'okta_email',
        'okta_profile_data',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            // Don't cast okta_profile_data here, we'll handle it manually for SQLite compatibility
        ];
    }

    /**
     * Get the Okta profile data attribute (decode JSON).
     */
    public function getOktaProfileDataAttribute($value)
    {
        if (is_null($value)) {
            return null;
        }

        if (is_array($value)) {
            return $value;
        }

        return json_decode($value, true);
    }

    /**
     * Set the Okta profile data attribute (encode to JSON).
     */
    public function setOktaProfileDataAttribute($value)
    {
        if (is_null($value)) {
            $this->attributes['okta_profile_data'] = null;
            return;
        }

        if (is_array($value)) {
            $this->attributes['okta_profile_data'] = json_encode($value);
            return;
        }

        $this->attributes['okta_profile_data'] = $value;
    }

    /**
     * Get the user sessions for this user.
     */
    public function userSessions()
    {
        return $this->hasMany(UserSession::class);
    }

    /**
     * Get the active user sessions for this user.
     */
    public function activeUserSessions()
    {
        return $this->hasMany(UserSession::class)->where('is_active', true);
    }

    /**
     * Get the most recent active session for a platform.
     */
    public function getActiveSessionForPlatform(string $platform = 'mobile')
    {
        return $this->activeUserSessions()
            ->where('platform', $platform)
            ->orderBy('last_activity_at', 'desc')
            ->first();
    }

    /**
     * Create or update user from Okta profile data.
     */
    public static function createOrUpdateFromOkta(array $oktaProfile): self
    {
        return static::updateOrCreate(
            ['okta_user_id' => $oktaProfile['sub']],
            [
                'name' => $oktaProfile['name'] ?? $oktaProfile['preferred_username'] ?? 'Unknown',
                'email' => $oktaProfile['email'] ?? $oktaProfile['preferred_username'],
                'okta_email' => $oktaProfile['email'] ?? null,
                'okta_profile_data' => $oktaProfile,
                'email_verified_at' => isset($oktaProfile['email_verified']) && $oktaProfile['email_verified'] ? now() : null,
            ]
        );
    }
}
