<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class UnaApiService
{
    public function get(string $endpoint = '/')
    {
        $url = rtrim(config('una.api_domain'), '/') . '/' . ltrim($endpoint, '/');
        $token = config('una.api_token');
        $response = Http::withToken($token)->get($url);

        return $response->json();
    }

    public function post(string $endpoint = '/', array $data = [])
    {
        $url = rtrim(config('una.api_url'), '/') . '/' . ltrim($endpoint, '/');
        $token = config('una.api_token');

        $response = Http::withToken($token)->post($url, $data);

        return $response->json();
    }
}
?>