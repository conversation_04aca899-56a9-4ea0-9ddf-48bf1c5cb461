<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class OktaService
{
    private Client $client;
    private string $domain;
    private string $authServerId;
    private string $clientId;
    private string $clientSecret;
    private string $redirectUri;

    public function __construct()
    {
        $this->client = new Client();
        $this->domain = config('services.okta.domain');
        $this->authServerId = config('services.okta.auth_server_id', 'default');
        $this->clientId = config('services.okta.client_id');
        $this->clientSecret = config('services.okta.client_secret');
        $this->redirectUri = config('services.okta.redirect_uri');
    }

    /**
     * Get the Okta issuer URL.
     */
    public function getIssuer(): string
    {
        return config('services.okta.issuer') ?: "https://{$this->domain}/oauth2/{$this->authServerId}";
    }

    /**
     * Generate PKCE code verifier and challenge.
     */
    public function generatePkceChallenge(): array
    {
        $codeVerifier = Str::random(128);
        $codeChallenge = rtrim(strtr(base64_encode(hash('sha256', $codeVerifier, true)), '+/', '-_'), '=');

        return [
            'code_verifier' => $codeVerifier,
            'code_challenge' => $codeChallenge,
        ];
    }

    /**
     * Build the Okta authorization URL.
     */
    public function buildAuthorizationUrl(string $state, array $pkce): string
    {
        $params = [
            'client_id' => $this->clientId,
            'response_type' => 'code',
            'scope' => 'openid profile email',
            'redirect_uri' => $this->redirectUri,
            'state' => $state,
            'code_challenge' => $pkce['code_challenge'],
            'code_challenge_method' => 'S256',
        ];

        return $this->getIssuer() . "/v1/authorize?" . http_build_query($params);
    }

    /**
     * Exchange authorization code for tokens.
     */
    public function exchangeCodeForTokens(string $code, string $codeVerifier): array
    {
        try {
            $response = $this->client->post($this->getIssuer() . "/v1/token", [
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'code' => $code,
                    'redirect_uri' => $this->redirectUri,
                    'code_verifier' => $codeVerifier,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!isset($data['access_token'])) {
                throw new \Exception('No access token received from Okta');
            }

            return $data;
        } catch (RequestException $e) {
            Log::error('Okta token exchange failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
            ]);
            throw new \Exception('Failed to exchange code for tokens: ' . $e->getMessage());
        }
    }

    /**
     * Get user profile from Okta using access token.
     */
    public function getUserProfile(string $accessToken): array
    {
        try {
            $response = $this->client->get($this->getIssuer() . "/v1/userinfo", [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Accept' => 'application/json',
                ],
            ]);

            $profile = json_decode($response->getBody()->getContents(), true);

            if (!isset($profile['sub'])) {
                throw new \Exception('Invalid user profile received from Okta');
            }

            return $profile;
        } catch (RequestException $e) {
            Log::error('Okta user profile fetch failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
            ]);
            throw new \Exception('Failed to fetch user profile: ' . $e->getMessage());
        }
    }

    /**
     * Refresh access token using refresh token.
     */
    public function refreshAccessToken(string $refreshToken): array
    {
        try {
            $response = $this->client->post($this->getIssuer() . "/v1/token", [
                'form_params' => [
                    'grant_type' => 'refresh_token',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'refresh_token' => $refreshToken,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!isset($data['access_token'])) {
                throw new \Exception('No access token received from Okta refresh');
            }

            return $data;
        } catch (RequestException $e) {
            Log::error('Okta token refresh failed', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
            ]);
            throw new \Exception('Failed to refresh token: ' . $e->getMessage());
        }
    }

    /**
     * Revoke tokens with Okta.
     */
    public function revokeToken(string $token, string $tokenType = 'access_token'): bool
    {
        try {
            $this->client->post($this->getIssuer() . "/v1/revoke", [
                'form_params' => [
                    'token' => $token,
                    'token_type_hint' => $tokenType,
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
            ]);

            return true;
        } catch (RequestException $e) {
            Log::error('Okta token revocation failed', [
                'error' => $e->getMessage(),
                'token_type' => $tokenType,
            ]);
            return false;
        }
    }

    /**
     * Validate token with Okta introspection endpoint.
     */
    public function introspectToken(string $token): array
    {
        try {
            $response = $this->client->post($this->getIssuer() . "/v1/introspect", [
                'form_params' => [
                    'token' => $token,
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            Log::error('Okta token introspection failed', [
                'error' => $e->getMessage(),
            ]);
            return ['active' => false];
        }
    }
}
